'use client';

import pbclient from "@/lib/db";
import { createContext, useContext, useEffect, useState } from "react";
import { logUserLogin, logUserLogout, logFailedLogin, logFailedPassReset, logPassResetSent } from "@/utils/auditLogger";
import { toast } from "sonner";

const authContext = createContext({});

export function AuthContextProvider({ children }) {
  const [user, setUser] = useState(pbclient.authStore.record);
  const [loading, setLoading] = useState(false);
  const [otpRequest, setOtpRequest] = useState();
  const [mfa, setMfa] = useState();

  useEffect(() => {
    const validateAuth = async () => {
      if (pbclient.authStore.isValid) {
        try {
          const refreshed = await pbclient.collection('users').authRefresh();
          setUser(refreshed.record);
        } catch (err) {
          console.warn("Auth token invalid or expired:", err);
          pbclient.authStore.clear();
          setUser(null);
          localStorage.removeItem('pocketbase_auth');
          toast.error('Session expired. Please log in again.');
        }
      } else {
        pbclient.authStore.clear();
        setUser(null);
      }
    };

    validateAuth();
  }, []);

  async function Login(emailOrUsername, password) {
    setLoading(true);
    try {
      if (emailOrUsername && emailOrUsername.trim() !== '') {
        const identity = emailOrUsername;
        let mfaId;

        // First verify credentials without full authentication
        try {
          await pbclient.collection('users').authWithPassword(identity, password);
        } catch (err) {
          mfaId = err.response?.mfaId;
          if (!mfaId) {
            setLoading(false);
            toast.error('Unauthorised email or password');
            await logFailedLogin(emailOrUsername);
            throw new Error('Unauthorised email or password');
          }
        }

        setMfa(mfaId)

        // If credentials are valid, request OTP
        localStorage.setItem('tryingEmail', identity);
        const req = await pbclient.collection('users').requestOTP(identity);
        setOtpRequest(req);
        setLoading(false);
        toast.success('OTP sent to your email');
        return { success: true }
      } else {
        setLoading(false);
        toast.error('Email or Username must not be empty!!!');
      }
    } catch (err) {
      setLoading(false);
      await logFailedLogin(emailOrUsername);
    }
  }

  async function ForgotPassword(email) {
    setLoading(true);
    try {
      const identity = email;
      console.log(email);
      if (!identity) {
        setLoading(false);
        toast.error('Session expired. Please login again.');
        throw new Error('Session expired. Please login again.');
      }
      await pbclient.collection('users').requestPasswordReset(identity);
      toast.success('Sent Password Reset Link on your email, please check!');
      logPassResetSent(email)
      setLoading(false);
      return { success: true }
    } catch (err) {
      setLoading(false);
      toast.error('Error sending Password Reset Link.');
      const identity = localStorage.getItem('tryingEmail');
      if (identity) {
        await logFailedPassReset(identity);
      }
    }
  }

  async function ResendOTP() {
    setLoading(true);
    try {
      const identity = localStorage.getItem('tryingEmail');
      if (!identity) {
        setLoading(false);
        toast.error('Session expired. Please login again.');
        throw new Error('Session expired. Please login again.');
      }
      const req = await pbclient.collection('users').requestOTP(identity);
      setOtpRequest(req);
      toast.success('OTP Sent!');
      setLoading(false);
      return { success: true }
    } catch (err) {
      setLoading(false);
      toast.error('Error Sending OTP.');
      const identity = localStorage.getItem('tryingEmail');
      if (identity) {
        await logFailedLogin(identity);
      }
    }
  }

  async function AuthWithOTP(otp) {
    setLoading(true);
    try {
      if (otp && otp.trim() !== '') {
        const identity = localStorage.getItem('tryingEmail');
        if (!identity) {
          setLoading(false);
          toast.error('Session expired. Please login again.');
          throw new Error('Session expired. Please login again.');
        }

        if (!otpRequest?.otpId) {
          setLoading(false);
          toast.error('OTP session expired. Please request a new OTP.');
          throw new Error('OTP session expired. Please request a new OTP.');
        }

        const res = await pbclient.collection('users').authWithOTP(otpRequest.otpId, otp, { 'mfaId': mfa });
        setUser(res.record);

        // Log successful login
        await logUserLogin(identity, res.record);

        // Clear temporary storage
        localStorage.removeItem('tryingEmail');
        setOtpRequest(null);

        setLoading(false);
        toast.success('Login successful!');
        return { success: true }
      } else {
        setLoading(false);
        toast.error('OTP must not be empty!!!');
        throw new Error('OTP must not be empty!!!');
      }
    } catch (err) {
      setLoading(false);
      toast.error('Invalid OTP. Please try again.');
      const identity = localStorage.getItem('tryingEmail');
      if (identity) {
        await logFailedLogin(identity);
      }
    }
  }

  async function Logout() {
    setLoading(true);
    try {
      const currentUser = user;

      // Log logout before clearing auth
      if (currentUser) {
        await logUserLogout(currentUser.email || currentUser.username, currentUser);
      }

      pbclient.authStore.clear();
      localStorage.removeItem('pocketbase_auth');
      setUser(null);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      toast.error(`Some error occured while logging you out`);
      console.error(`${err?.message}`);
    }
  }

  const value = {
    user,
    loading,
    Login,
    AuthWithOTP,
    ResendOTP,
    ForgotPassword,
    Logout,
  };

  return (
    <authContext.Provider value={value}>
      {children}
    </authContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(authContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthLayoutProvider')
  }
  return context;
}

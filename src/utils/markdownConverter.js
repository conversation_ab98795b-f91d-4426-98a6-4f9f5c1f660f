import matter from 'gray-matter';
import { marked } from 'marked';
import { notFound } from 'next/navigation';
import rehypeDocument from 'rehype-document';
import rehypeFormat from 'rehype-format';
import rehypeStringify from 'rehype-stringify';
import remarkParse from 'remark-parse';
import remarkRehype from 'remark-rehype';
import rehypePrettyCode from 'rehype-pretty-code';
import { unified } from 'unified'
import { transformerCopyButton } from '@rehype-pretty/transformers';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import rehypeSlug from 'rehype-slug';

export async function markdownAsHtml(url) {
  try {
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Failed to fetch markdown from ${url}`);

    const markdown = await res.text();
    const htmlContent = marked(markdown);

    return {
      html: htmlContent,
      raw: markdown
    };
  } catch (error) {
    console.log(error);
    notFound();
  }
}

export async function markdownAsHtml2({ url, title }) {
  try {
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Failed to fetch markdown from ${url}`);

    const markdown = await res.text();
    const { content } = matter(markdown);

    const processor = unified()
      .use(remarkParse)
      .use(remarkRehype)
      .use(rehypeDocument, { title: title })
      .use(rehypeFormat)
      .use(rehypeStringify)
      .use(rehypeSlug)
      .use(rehypeAutolinkHeadings)
      .use(rehypePrettyCode, {
        theme: 'tokyo-night',
        transformers: [
          transformerCopyButton({
            visibility: 'always',
            feedbackDuration: 3_000,
          }),
        ],
      });
    ;

    const htmlContent = (await processor.process(content)).toString();
    return {
      html: htmlContent,
      raw: content
    };

  } catch (error) {
    console.log(error);
    notFound();
  }
}

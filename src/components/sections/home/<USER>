'use client';

import { motion } from 'motion/react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Clock, Eye, Star, TrendingUp, ArrowUpRight } from 'lucide-react';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';

export function FeaturedPosts({ posts }) {
  if (!posts || posts.length === 0) return null;

  const mainFeatured = posts[0];
  const secondaryFeatured = posts.slice(1, 3);

  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="min-h-screen flex items-center justify-center py-10"
    >
      <div className="container-modern">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex items-center justify-center gap-2 mb-4"
          >
            <h2 className="text-headline text-shimmer">Featured Stories</h2>
          </motion.div>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-muted-foreground text-lg max-w-2xl mx-auto"
          >
            Handpicked articles that showcase the best of our content
          </motion.p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Main Featured Post */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Card className="h-full overflow-hidden border-0 shadow-2xl backdrop-blur-sm group hover:shadow-3xl transition-all duration-500 p-0 min-h-[450px]">
              <div className="relative overflow-hidden h-full">
                {/* Background Image */}
                <LazyLoadingImage
                  src={mainFeatured.image}
                  alt={mainFeatured.title}
                  className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

                {/* Featured badge & Stats overlay */}
                <div className="absolute top-6 left-6 flex md:flex-row flex-col md:items-center justify-between gap-4">
                  <Badge className="bg-gradient-to-r from-primary to-primary/80 text-white border-0 px-3 py-1">
                    <Star className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                  <div className="flex gap-2">
                    <Badge variant="secondary" className="bg-black/70 text-white border-0 backdrop-blur-sm">
                      <Eye className="w-3 h-3 mr-1" />
                      {mainFeatured.views}
                    </Badge>
                    <Badge variant="secondary" className="bg-black/70 text-white border-0 backdrop-blur-sm">
                      <Clock className="w-3 h-3 mr-1" />
                      {mainFeatured.readTime}
                    </Badge>
                  </div>
                </div>

                {/* Content overlay */}
                <div className="absolute bottom-6 left-6 right-6 text-white">
                  <Badge variant="outline" className="bg-white/20 text-white border-white/30 mb-3">
                    {mainFeatured.category}
                  </Badge>
                  <h3 className="md:text-2xl text-lg font-bold mb-3">
                    {mainFeatured.title}
                  </h3>
                  <p className="text-white/90 md:text-base text-sm line-clamp-2 mb-4">
                    {mainFeatured.description}
                  </p>
                  <div className="flex md:flex-row flex-col gap-2 md:items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8 ring-2 ring-white/20">
                        <AvatarImage src={mainFeatured.author?.avatar} />
                        <AvatarFallback>
                          {mainFeatured.author?.first_name?.charAt(0)}
                          {mainFeatured.author?.last_name?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">
                          {mainFeatured.author?.first_name} {mainFeatured.author?.last_name}
                        </p>
                        <p className="text-xs text-white/70">{mainFeatured.publishedAt}</p>
                      </div>
                    </div>
                    <Button size="sm" variant="secondary" className="bg-white/90 text-black hover:bg-white">
                      Read More
                      <ArrowUpRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Secondary Featured Posts */}
          <div className="flex flex-col gap-4 justify-between">
            {secondaryFeatured.map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm group lg:h-50 p-0">
                  <div className="md:flex h-full">
                    <div className="relative lg:w-50 lg:h-50 h-50  overflow-hidden flex-shrink-0">
                      {/* Background Image */}
                      <LazyLoadingImage
                        src={post.image}
                        alt={post.title}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                      />
                      {/* Overlay */}
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="bg-black/70 text-white border-0 text-xs">
                          <TrendingUp className="w-2 h-2 mr-1" />
                          Trending
                        </Badge>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-3 flex flex-col justify-between min-h-0">
                      <div className="flex-1">
                        <div className="flex items-center justify-between gap-2 mb-4">
                          <Badge variant="outline" className="text-xs">
                            {post.category}
                          </Badge>
                          <span className="text-xs text-muted-foreground">{post.readTime}</span>
                        </div>
                        <h4 className="font-semibold mb-2 group-hover:text-primary transition-colors text-sm leading-tight">
                          {post.title}
                        </h4>
                        <p className="text-xs text-muted-foreground">
                          {post.description}
                        </p>
                      </div>
                      <div className="flex items-center justify-between pt-2 mt-auto">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-5 w-5">
                            <AvatarImage src={post.author?.avatar} />
                            <AvatarFallback className="text-xs">
                              {post.author?.first_name?.charAt(0)}
                              {post.author?.last_name?.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-xs text-muted-foreground truncate">
                            {post.author?.first_name} {post.author?.last_name}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Eye className="w-3 h-3" />
                          {post.views}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.section>
  );
}

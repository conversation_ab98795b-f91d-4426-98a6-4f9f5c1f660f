import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AUTHORS } from '@/constants/authors';

export default function ContactPreview() {
  return (
    <div className="bg-gradient-to-br from-card to-card/80 rounded-2xl p-6 backdrop-blur-sm border">
      <h3 className="font-semibold mb-4">Meet Your Team</h3>
      <div className="flex -space-x-3 mb-4">
        {AUTHORS.map((member, index) => (
          <Avatar key={index} className="w-12 h-12 ring-2 ring-background">
            <AvatarImage src={member?.avatar} alt={member.name} />
            <AvatarFallback>
              {member?.first_name.split(' ').map(n => n[0]).join('')}
              {member?.last_name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
        ))}
      </div>
      <p className="text-sm text-muted-foreground">
        Our expert team is ready to bring your vision to life with creativity,
        technical excellence, and strategic thinking.
      </p>
    </div>
  )
};

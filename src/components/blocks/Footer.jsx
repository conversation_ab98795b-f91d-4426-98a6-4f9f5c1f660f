import { categories } from '@/constants/categories';
import { CompanyName } from '@/constants/companyName';
import { Github, Twitter, Linkedin, Mail, Zap, Heart } from 'lucide-react';
import Link from 'next/link';

export function Footer() {
  return (
    <footer className="bg-footer-gradient text-[#f7f7f7] relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-12">
          <path d="M0 0v46.29c47.79 22.2 103.59 32.17 158 28 70.36-5.37 136.33-33.31 206.8-37.5 73.84-4.36 147.54 16.88 218.2 35.26 69.27 18 138.3 24.88 209.4 13.08 36.15-6 69.85-17.84 104.45-29.34C989.49 25 1113-14.29 1200 52.47V0z"
            fill="#f7f7f7" opacity="0.1"></path>
        </svg>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-[#168B94] rounded-xl flex items-center justify-center">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white">
                {CompanyName}
              </h2>
            </div>
            <p className="text-[#D9CAB3] mb-8 max-w-md leading-relaxed text-lg">
              High-quality content across tech and lifestyle niches for professionals,
              tech enthusiasts, and curious learners seeking innovation.
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-[#D9CAB3] hover:text-[#168B94] transition-all duration-300 transform hover:scale-110">
                <Github className="h-6 w-6" />
              </a>
              <a href="#" className="text-[#D9CAB3] hover:text-[#168B94] transition-all duration-300 transform hover:scale-110">
                <Twitter className="h-6 w-6" />
              </a>
              <a href="#" className="text-[#D9CAB3] hover:text-[#168B94] transition-all duration-300 transform hover:scale-110">
                <Linkedin className="h-6 w-6" />
              </a>
              <a href="#" className="text-[#D9CAB3] hover:text-[#168B94] transition-all duration-300 transform hover:scale-110">
                <Mail className="h-6 w-6" />
              </a>
            </div>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-white font-semibold mb-6 text-lg">Categories</h3>
            <ul className="space-y-3">
              {categories.map((category, index) => (
                <li key={index}>
                  <Link
                    href={`#${category?.title?.toLowerCase()}`}
                    className="text-[#D9CAB3] hover:text-[#168B94] transition-colors duration-300 text-sm font-medium relative group"
                  >
                    {category?.title}
                    <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-[#168B94] transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-6 text-lg">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <a href="#about" className="text-[#D9CAB3] hover:text-[#168B94] transition-colors duration-300 text-sm font-medium relative group">
                  About Us
                  <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-[#168B94] transition-all duration-300 group-hover:w-full"></span>
                </a>
              </li>
              <li>
                <a href="#contact" className="text-[#D9CAB3] hover:text-[#168B94] transition-colors duration-300 text-sm font-medium relative group">
                  Contact
                  <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-[#168B94] transition-all duration-300 group-hover:w-full"></span>
                </a>
              </li>
              <li>
                <a href="#privacy" className="text-[#D9CAB3] hover:text-[#168B94] transition-colors duration-300 text-sm font-medium relative group">
                  Privacy Policy
                  <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-[#168B94] transition-all duration-300 group-hover:w-full"></span>
                </a>
              </li>
              <li>
                <a href="#terms" className="text-[#D9CAB3] hover:text-[#168B94] transition-colors duration-300 text-sm font-medium relative group">
                  Terms of Service
                  <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-[#168B94] transition-all duration-300 group-hover:w-full"></span>
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-[#4C5C68]/30 mt-12 pt-8 text-center">
          <p className="text-[#D9CAB3] text-sm flex items-center justify-center space-x-2">
            <span>© 2025 {CompanyName}. Made with</span>
            <Heart className="h-4 w-4 text-[#168B94] fill-current" />
            <span>by Creative Team. All rights reserved.</span>
          </p>
        </div>
      </div>
    </footer>
  );
}

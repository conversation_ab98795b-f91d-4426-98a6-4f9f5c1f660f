"use client"
import React, { useEffect, useState } from 'react';
import parse from 'html-react-parser';
import Link from 'next/link';

const OnThisPage = ({ htmlContent }) => {
  const [headings, setHeadings] = useState([]);

  useEffect(() => {
    // Parse the HTML content and extract h2 headings
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    const h2Elements = tempDiv.querySelectorAll('h2');
    const h2Data = Array.from(h2Elements).map(h2 => ({
      text: h2.textContent,
      id: h2.id
    }));
    setHeadings(h2Data);
  }, [htmlContent]);

  return (
    <aside
      className="hidden lg:block sticky right-8 top-24 min-w-64 bg-background dark:bg-accent shadow-lg border border-gray-200 dark:border-gray-700 rounded-lg p-4 overflow-y-auto max-h-[80vh]"
    >
      <h2 className="font-semibold uppercase tracking-wide mb-3">
        Table of Contents
      </h2>
      <ul className="space-y-2 text-sm">
        {headings.map((heading, index) => (
          <li key={index}>
            <Link
              href={`#${heading.id}`}
              className={'cursor-pointer hover:underline underline-offset-4 text-foreground/60 hover:text-primary transition-all duration-300'}
            >
              {heading.text}
            </Link>
          </li>
        ))}
      </ul>
    </aside>
  );
};

export default OnThisPage;

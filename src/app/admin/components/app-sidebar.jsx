"use client"

import * as React from "react"
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Cog,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  NotebookText,
  PieChart,
  Settings2,
  SquareTerminal,
  Zap,
} from "lucide-react"

import { NavMain } from "./nav-main"
import { NavProjects } from "./nav-projects"
import { NavUser } from "./nav-user"
import { TeamSwitcher } from "./team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useAuth } from "@/contexts/AuthContext"
import pbclient from "@/lib/db"
import { useCollection } from "@/hooks/useCollection"
import { CompanyName } from "@/constants/companyName"

// This is sample data.
const data = {
  team: {
    name: CompanyName,
    logo: Zap,
  },
  navMain: [
    {
      title: 'Blogs',
      url: '',
      icon: NotebookText,
      isActive: true,
      items: [
        { title: 'All', url: '/admin/blogs' },
        { title: 'Featured', url: '/admin/blogs/featured' },
      ],
    },
    {
      title: 'Admin',
      url: '',
      icon: SquareTerminal,
      isActive: true,
      items: [
        { title: 'Dashboard', url: '/admin/dashboard' },
        { title: 'Profile', url: '/admin/profile' },
        { title: 'Tags', url: '/admin/tags' },
        { title: 'Services', url: '/admin/services' },
        { title: 'Categories', url: '/admin/categories' },
        { title: 'Users', url: '/admin/users' },
      ],
    },
    {
      title: 'General',
      url: '',
      icon: Cog,
      isActive: true,
      items: [
        { title: 'Inquiries', url: '/admin/inquiries' },
        { title: 'Newsletters', url: '/admin/newsletters' },
        { title: 'Settings', url: '/admin/settings' },
        { title: 'Backup & Restore', url: '/admin/backup' },
      ],
    },
  ],
}

export function AppSidebar({ ...props }) {
  const { user } = useAuth();
  const [userInfo, setUserInfo] = React.useState({
    url: '',
    initials: '',
    fullName: '',
    email: '',
  });

  // Fetch user profile data
  const {
    data: userProfile,
  } = useCollection('user_profiles', {
    filter: `user.id = "${user?.id}"`,
    expand: 'user'
  });
  const currentProfile = React.useMemo(() => userProfile?.[0] || null, [userProfile]);


  React.useEffect(() => {
    const getAvatarUrl = () => {
      if (user?.avatar) {
        return pbclient.files.getURL(user, user.avatar);
      }
      return null;
    };

    const getInitials = () => {
      if (currentProfile?.firstname && currentProfile?.lastname) {
        return `${currentProfile.firstname.charAt(0)}${currentProfile.lastname.charAt(0)}`.toUpperCase();
      }
      return user?.email?.charAt(0).toUpperCase() || 'U';
    };

    const getFullName = () => {
      if (currentProfile?.firstName && currentProfile?.lastName) {
        return `${currentProfile.firstName} ${currentProfile.lastName}`;
      }
    };

    if (user?.id) {
      const url = getAvatarUrl();
      const initials = getInitials();
      const fullName = getFullName();
      setUserInfo({ url, initials, fullName, email: user?.email })
    }

  }, [user]);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.team} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userInfo} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
};

import { DataTable } from '@/components/ui/data-table';
import { useCollection } from '@/hooks/useCollection'
import React from 'react'
import Form from './Form';
import EditForm from './EditForm';
import MobileDataTable from '@/components/ui/mobile-data-table';
import { useIsMobile } from '@/hooks/use-mobile';
import DetailsActions from '@/components/actions-buttons/DetailsActions';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import { Badge } from '@/components/ui/badge';
import pbclient from '@/lib/db';

export default function ServicesLayout() {
  const { data, deleteItem } = useCollection('services', {
    expand: 'ownedBy',
  });

  const columns = [
    {
      id: 'id',
      accessorKey: 'id',
      header: 'Service ID',
      filterable: true,
      cell: ({ row }) => <div>{row.original.id}</div>,
    },
    {
      id: 'icon',
      accessorKey: 'icon',
      header: 'Icon',
      filterable: true,
      cell: ({ row }) => {
        const icon = row.original?.icon;
        if (!icon) return null;
        const url = pbclient.files.getURL(row.original, icon);
        return (
          <LazyLoadingImage src={url} alt={row.original?.title || 'icon'} className="w-8 h-8 rounded-md object-cover" />
        );
      }
    },
    {
      id: 'title',
      accessorKey: 'title',
      header: 'Title',
      filterable: true,
      cell: ({ row }) => <div>{row.original?.title}</div>,
    },
    {
      id: 'description',
      accessorKey: 'description',
      header: 'Description',
      filterable: true,
      cell: ({ row }) => <div>{row.original.description}</div>,
    },
    {
      id: 'features',
      accessorKey: 'features',
      header: 'Features',
      filterable: true,
      cell: ({ row }) => {
        return (
          <div className={`grid gap-4 p-2`}>
            {row.original?.features?.map((feature, index) => (
              <Badge variant={'outline'} key={index}>
                {feature}
              </Badge>
            ))}
          </div>
        )
      }
    },
    {
      id: 'actions',
      accessorKey: 'actions',
      header: 'Actions',
      filterable: false,
      cell: ({ row }) => (
        <DetailsActions
          row={row}
          EditForm={EditForm}
          deleteItem={deleteItem}
          showEye={false}
        />
      ),
    }
  ];

  return (
    <>
      {
        useIsMobile() ? (
          <div className="border-2 bg-background md:p-4 rounded-xl mt-8">
            <h1 className="text-xl font-semibold p-4">Services</h1>
            <div className="flex justify-end p-4">
              <Form />
            </div>
            <MobileDataTable
              columns={columns}
              data={data}
            />
          </div>
        ) : (
          <div className="border-2 bg-background dark:bg-accent md:p-4 rounded-xl mt-8">
            <div className="flex items-center justify-between gap-4">
              <h1 className="text-lg font-semibold">Services</h1>
              <Form />
            </div>

            <DataTable
              columns={columns}
              data={data}
            />
          </div>
        )
      }
    </>
  )
};

'use client';
import React, { useMemo, useState } from "react";
import { useCollection } from "@/hooks/useCollection";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  Search,
  Plus,
  Mail,
  Users,
  UserCheck,
  UserX,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  MailOpen,
  UserPlus,
  Download
} from "lucide-react";

export default function NewslettersPage() {
  const STATUSES = [
    { 
      value: "new_subscribers", 
      label: "New Subscribers", 
      icon: UserPlus, 
      color: "bg-blue-500",
      filter: (subscriber) => subscriber.subscribed && isNewSubscriber(subscriber)
    },
    { 
      value: "active_subscribers", 
      label: "Active Subscribers", 
      icon: UserCheck, 
      color: "bg-green-500",
      filter: (subscriber) => subscriber.subscribed && !isNewSubscriber(subscriber)
    },
    { 
      value: "pending_verification", 
      label: "Pending Verification", 
      icon: Clock, 
      color: "bg-yellow-500",
      filter: (subscriber) => !subscriber.subscribed && isNewSubscriber(subscriber)
    },
    { 
      value: "unsubscribed", 
      label: "Unsubscribed", 
      icon: UserX, 
      color: "bg-red-500",
      filter: (subscriber) => !subscriber.subscribed && !isNewSubscriber(subscriber)
    },
  ];

  // Helper function to determine if subscriber is new (within last 7 days)
  function isNewSubscriber(subscriber) {
    const createdDate = new Date(subscriber.created);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return createdDate > sevenDaysAgo;
  }

  // Fetch newsletter subscribers from PocketBase
  const { data: subscribers, createItem, updateItem, deleteItem, error: subscribersError } = useCollection('newsletter', {
    sort: '-created'
  });

  const [query, setQuery] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSubscriber, setEditingSubscriber] = useState(null);
  const [formData, setFormData] = useState({
    email: "",
    subscribed: true
  });

  // Reset form
  const resetForm = () => {
    setFormData({
      email: "",
      subscribed: true
    });
    setEditingSubscriber(null);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    try {
      if (editingSubscriber) {
        await updateItem(editingSubscriber.id, formData);
        toast.success("Subscriber updated successfully");
      } else {
        // Check for duplicate email when creating new subscriber
        const existingSubscriber = subscribers?.find(s => s.email === formData.email);
        if (existingSubscriber) {
          toast.error("A subscriber with this email already exists");
          return;
        }

        await createItem(formData);
        toast.success("Subscriber added successfully");
      }
      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      toast.error("Failed to save subscriber");
      console.error(error);
    }
  };

  // Handle edit
  const handleEdit = (subscriber) => {
    setEditingSubscriber(subscriber);
    setFormData({
      email: subscriber.email || "",
      subscribed: subscriber.subscribed || false
    });
    setIsDialogOpen(true);
  };

  // Handle delete
  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this subscriber?")) {
      try {
        await deleteItem(id);
        toast.success("Subscriber deleted successfully");
      } catch (error) {
        toast.error("Failed to delete subscriber");
        console.error(error);
      }
    }
  };

  // Handle subscription status change
  const handleStatusChange = async (subscriberId, newStatus) => {
    try {
      const subscribed = newStatus === 'active_subscribers' || newStatus === 'new_subscribers';
      await updateItem(subscriberId, { subscribed });
      toast.success("Subscription status updated successfully");
    } catch (error) {
      toast.error("Failed to update subscription status");
      console.error(error);
    }
  };

  // Handle bulk unsubscribe for unsubscribed users
  const handleBulkCleanup = async () => {
    const unsubscribedUsers = subscribers?.filter(s => !s.subscribed) || [];
    if (unsubscribedUsers.length === 0) {
      toast.info("No unsubscribed users to remove");
      return;
    }

    if (confirm(`Are you sure you want to delete ${unsubscribedUsers.length} unsubscribed users? This action cannot be undone.`)) {
      try {
        const deletePromises = unsubscribedUsers.map(user => deleteItem(user.id));
        await Promise.all(deletePromises);
        toast.success(`Successfully removed ${unsubscribedUsers.length} unsubscribed users`);
      } catch (error) {
        toast.error("Failed to remove unsubscribed users");
        console.error(error);
      }
    }
  };

  // Handle export subscribers
  const handleExportSubscribers = () => {
    if (!subscribers || subscribers.length === 0) {
      toast.info("No subscribers to export");
      return;
    }

    const activeSubscribers = subscribers.filter(s => s.subscribed);
    if (activeSubscribers.length === 0) {
      toast.info("No active subscribers to export");
      return;
    }

    const csvContent = [
      "Email,Subscribed,Created Date",
      ...activeSubscribers.map(s =>
        `${s.email},${s.subscribed ? 'Yes' : 'No'},${new Date(s.created).toLocaleDateString()}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast.success(`Exported ${activeSubscribers.length} active subscribers`);
  };

  // Filter subscribers based on search query
  const filteredSubscribers = useMemo(() => {
    if (!subscribers) return [];
    if (!query) return subscribers;
    
    const q = query.toLowerCase();
    return subscribers.filter(
      (subscriber) =>
        subscriber.email?.toLowerCase().includes(q)
    );
  }, [subscribers, query]);

  // Group subscribers by status
  const groupedSubscribers = useMemo(() => {
    const grouped = {};
    STATUSES.forEach((status) => {
      grouped[status.value] = filteredSubscribers.filter(status.filter);
    });
    return grouped;
  }, [filteredSubscribers]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!subscribers) return { total: 0, active: 0, pending: 0, unsubscribed: 0, newSubscribers: 0 };
    
    const active = subscribers.filter(s => s.subscribed).length;
    const unsubscribed = subscribers.filter(s => !s.subscribed).length;
    const newSubscribers = subscribers.filter(s => isNewSubscriber(s) && s.subscribed).length;
    const pending = subscribers.filter(s => !s.subscribed && isNewSubscriber(s)).length;
    
    return {
      total: subscribers.length,
      active,
      pending,
      unsubscribed,
      newSubscribers
    };
  }, [subscribers]);

  // Show error state if there's an error
  if (subscribersError) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-6 text-center">
            <CardContent>
              <XCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
              <h3 className="text-lg font-semibold mb-2">Error Loading Data</h3>
              <p className="text-muted-foreground">
                {subscribersError?.message || "Failed to load newsletter data"}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Show loading state
  if (!subscribers) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <div className="h-8 w-64 bg-muted rounded loading-skeleton mb-2"></div>
            <div className="h-4 w-96 bg-muted rounded loading-skeleton"></div>
          </div>
          <div className="flex gap-3">
            <div className="h-9 w-32 bg-muted rounded loading-skeleton"></div>
            <div className="h-9 w-32 bg-muted rounded loading-skeleton"></div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 lg:gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="glass">
              <CardHeader className="pb-2">
                <div className="h-4 w-20 bg-muted rounded loading-skeleton"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-12 bg-muted rounded loading-skeleton"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="glass">
              <CardHeader className="pb-4">
                <div className="h-6 w-24 bg-muted rounded loading-skeleton"></div>
              </CardHeader>
              <CardContent className="space-y-3">
                {Array.from({ length: 3 }).map((_, j) => (
                  <div key={j} className="h-20 bg-muted rounded loading-skeleton"></div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-headline">Newsletter Management</h1>
          <p className="text-muted-foreground">
            Manage newsletter subscribers and track subscription status across your mailing list.
          </p>
        </div>
        
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          <div className="relative flex-1 sm:flex-none">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search email addresses..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10 w-full sm:w-[280px] lg:w-[320px]"
            />
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleExportSubscribers}
              className="btn-modern"
              disabled={!subscribers?.some(s => s.subscribed)}
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>

            <Button
              variant="outline"
              onClick={handleBulkCleanup}
              className="btn-modern"
              disabled={!subscribers?.some(s => !s.subscribed)}
            >
              <UserX className="h-4 w-4" />
              Cleanup
            </Button>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm} className="btn-modern">
                  <Plus className="h-4 w-4" />
                  Add Subscriber
                </Button>
              </DialogTrigger>
            <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto mx-4 sm:mx-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingSubscriber ? "Edit Subscriber" : "Add New Subscriber"}
                </DialogTitle>
              </DialogHeader>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    placeholder="Enter email address"
                    required
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="subscribed">Subscription Status</Label>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {formData.subscribed ? "Subscribed" : "Unsubscribed"}
                    </span>
                    <Switch
                      id="subscribed"
                      checked={formData.subscribed}
                      onCheckedChange={(checked) => setFormData({ ...formData, subscribed: checked })}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingSubscriber ? "Update" : "Add"} Subscriber
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 lg:gap-4">
        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <Users className="h-4 w-4" />
              Total Subscribers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <UserCheck className="h-4 w-4" />
              Active
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              New This Week
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.newSubscribers}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Pending
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <UserX className="h-4 w-4" />
              Unsubscribed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.unsubscribed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Kanban Board */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
        {STATUSES.map((status) => {
          const StatusIcon = status.icon;
          const statusSubscribers = groupedSubscribers[status.value] || [];

          return (
            <Card key={status.value} className="glass">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 rounded-lg ${status.color} text-white`}>
                      <StatusIcon className="h-4 w-4" />
                    </div>
                    <span className="text-sm font-medium">{status.label}</span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {statusSubscribers.length}
                  </Badge>
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-3">
                {statusSubscribers.map((subscriber) => (
                  <Card key={subscriber.id} className="card-hover border-border/50">
                    <CardContent className="p-3 sm:p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <Mail className="h-3 w-3 text-muted-foreground shrink-0" />
                              <span className="font-medium text-sm truncate">
                                {subscriber.email}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              {subscriber.subscribed ? (
                                <Badge variant="default" className="text-xs">
                                  <CheckCircle className="h-2 w-2 mr-1" />
                                  Subscribed
                                </Badge>
                              ) : (
                                <Badge variant="secondary" className="text-xs">
                                  <XCircle className="h-2 w-2 mr-1" />
                                  Unsubscribed
                                </Badge>
                              )}
                              {isNewSubscriber(subscriber) && (
                                <Badge variant="outline" className="text-xs">
                                  New
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="flex gap-1 shrink-0">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleEdit(subscriber)}
                              className="h-7 w-7 p-0 hover:bg-accent"
                              title="Edit subscriber"
                            >
                              <MailOpen className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleDelete(subscriber.id)}
                              className="h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                              title="Delete subscriber"
                            >
                              <XCircle className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-2 border-t border-border/50">
                          <div className="text-xs text-muted-foreground">
                            {new Date(subscriber.created).toLocaleDateString()}
                          </div>

                          <Select
                            value={subscriber.subscribed ?
                              (isNewSubscriber(subscriber) ? 'new_subscribers' : 'active_subscribers') :
                              (isNewSubscriber(subscriber) ? 'pending_verification' : 'unsubscribed')
                            }
                            onValueChange={(newStatus) => handleStatusChange(subscriber.id, newStatus)}
                          >
                            <SelectTrigger className="h-6 w-auto text-xs border-none p-1">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {STATUSES.map((s) => (
                                <SelectItem key={s.value} value={s.value} className="text-xs">
                                  {s.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {statusSubscribers.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <StatusIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No subscribers</p>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

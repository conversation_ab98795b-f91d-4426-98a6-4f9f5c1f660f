'use client';
import React, { useMemo, useState } from "react";
import { useCollection } from "@/hooks/useCollection";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import {
  Search,
  Plus,
  Mail,
  Building,
  DollarSign,
  Calendar,
  User,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp
} from "lucide-react";

export default function InquiriesPage() {
  const STATUSES = [
    { value: "pending", label: "Pending", icon: Clock, color: "bg-yellow-500" },
    { value: "in_progress", label: "In Progress", icon: AlertCircle, color: "bg-blue-500" },
    { value: "deal", label: "Deal", icon: CheckCircle, color: "bg-green-500" },
    { value: "completed", label: "Completed", icon: CheckCircle, color: "bg-emerald-500" },
    { value: "no_deal", label: "No Deal", icon: XCircle, color: "bg-red-500" },
  ];

  // Fetch inquiries and services from PocketBase
  const { data: inquiries, createItem, updateItem, deleteItem, error: inquiriesError } = useCollection('inquiries', {
    expand: 'service',
    sort: '-created'
  });
  const { data: services, error: servicesError } = useCollection('services');

  const [query, setQuery] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingInquiry, setEditingInquiry] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    companyName: "",
    service: "",
    budget: "",
    timeline: "",
    description: "",
    status: "pending"
  });

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      companyName: "",
      service: "",
      budget: "",
      timeline: "",
      description: "",
      status: "pending"
    });
    setEditingInquiry(null);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingInquiry) {
        await updateItem(editingInquiry.id, formData);
        toast.success("Inquiry updated successfully");
      } else {
        await createItem(formData);
        toast.success("Inquiry created successfully");
      }
      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      toast.error("Failed to save inquiry");
      console.error(error);
    }
  };

  // Handle edit
  const handleEdit = (inquiry) => {
    setEditingInquiry(inquiry);
    setFormData({
      name: inquiry.name || "",
      email: inquiry.email || "",
      companyName: inquiry.companyName || "",
      service: inquiry.service || "",
      budget: inquiry.budget || "",
      timeline: inquiry.timeline || "",
      description: inquiry.description || "",
      status: inquiry.status || "pending"
    });
    setIsDialogOpen(true);
  };

  // Handle delete
  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this inquiry?")) {
      try {
        await deleteItem(id);
        toast.success("Inquiry deleted successfully");
      } catch (error) {
        toast.error("Failed to delete inquiry");
        console.error(error);
      }
    }
  };

  // Filter inquiries based on search query
  const filteredInquiries = useMemo(() => {
    if (!inquiries) return [];
    if (!query) return inquiries;

    const q = query.toLowerCase();
    return inquiries.filter(
      (inquiry) =>
        inquiry.name?.toLowerCase().includes(q) ||
        inquiry.email?.toLowerCase().includes(q) ||
        inquiry.companyName?.toLowerCase().includes(q) ||
        inquiry.description?.toLowerCase().includes(q)
    );
  }, [inquiries, query]);

  // Group inquiries by status
  const groupedInquiries = useMemo(() => {
    const grouped = {};
    STATUSES.forEach((status) => {
      grouped[status.value] = filteredInquiries.filter(
        (inquiry) => inquiry.status === status.value
      );
    });
    return grouped;
  }, [filteredInquiries]);

  // Handle status change
  const handleStatusChange = async (inquiryId, newStatus) => {
    try {
      await updateItem(inquiryId, { status: newStatus });
      toast.success("Status updated successfully");
    } catch (error) {
      toast.error("Failed to update status");
      console.error(error);
    }
  };

  // Calculate statistics
  const stats = useMemo(() => {
    if (!inquiries) return { total: 0, pending: 0, inProgress: 0, deals: 0, completed: 0 };

    return {
      total: inquiries.length,
      pending: inquiries.filter(i => i.status === 'pending').length,
      inProgress: inquiries.filter(i => i.status === 'in_progress').length,
      deals: inquiries.filter(i => i.status === 'deal').length,
      completed: inquiries.filter(i => i.status === 'completed').length,
    };
  }, [inquiries]);

  // Show error state if there's an error
  if (inquiriesError || servicesError) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-6 text-center">
            <CardContent>
              <XCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
              <h3 className="text-lg font-semibold mb-2">Error Loading Data</h3>
              <p className="text-muted-foreground">
                {inquiriesError?.message || servicesError?.message || "Failed to load inquiries data"}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Show loading state
  if (!inquiries || !services) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <div className="h-8 w-64 bg-muted rounded loading-skeleton mb-2"></div>
            <div className="h-4 w-96 bg-muted rounded loading-skeleton"></div>
          </div>
          <div className="flex gap-3">
            <div className="h-9 w-32 bg-muted rounded loading-skeleton"></div>
            <div className="h-9 w-32 bg-muted rounded loading-skeleton"></div>
          </div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 lg:gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="glass">
              <CardHeader className="pb-2">
                <div className="h-4 w-20 bg-muted rounded loading-skeleton"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-12 bg-muted rounded loading-skeleton"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 lg:gap-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="glass">
              <CardHeader className="pb-4">
                <div className="h-6 w-24 bg-muted rounded loading-skeleton"></div>
              </CardHeader>
              <CardContent className="space-y-3">
                {Array.from({ length: 3 }).map((_, j) => (
                  <div key={j} className="h-20 bg-muted rounded loading-skeleton"></div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-headline">Inquiries Management</h1>
          <p className="text-muted-foreground">
            Manage customer inquiries and track their progress through the sales pipeline.
          </p>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          <div className="relative flex-1 sm:flex-none">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search inquiries..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10 w-full sm:w-[280px] lg:w-[320px]"
            />
          </div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm} className="btn-modern">
                <Plus className="h-4 w-4" />
                New Inquiry
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto mx-4 sm:mx-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingInquiry ? "Edit Inquiry" : "Create New Inquiry"}
                </DialogTitle>
              </DialogHeader>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter name"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      placeholder="Enter email"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Company Name</Label>
                    <Input
                      id="companyName"
                      value={formData.companyName}
                      onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
                      placeholder="Enter company name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="service">Service</Label>
                    <Select
                      value={formData.service}
                      onValueChange={(value) => setFormData({ ...formData, service: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a service" />
                      </SelectTrigger>
                      <SelectContent>
                        {services?.map((service) => (
                          <SelectItem key={service.id} value={service.id}>
                            {service.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="budget">Budget</Label>
                    <Input
                      id="budget"
                      value={formData.budget}
                      onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                      placeholder="Enter budget"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timeline">Timeline</Label>
                    <Input
                      id="timeline"
                      value={formData.timeline}
                      onChange={(e) => setFormData({ ...formData, timeline: e.target.value })}
                      placeholder="Enter timeline"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => setFormData({ ...formData, status: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {STATUSES.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter description"
                    rows={4}
                  />
                </div>

                <div className="flex justify-end gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingInquiry ? "Update" : "Create"} Inquiry
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 lg:gap-4">
        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Total Inquiries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Pending
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              In Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Deals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.deals}</div>
          </CardContent>
        </Card>

        <Card className="glass card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Completed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">{stats.completed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Kanban Board */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 lg:gap-6">
        {STATUSES.map((status) => {
          const StatusIcon = status.icon;
          const statusInquiries = groupedInquiries[status.value] || [];

          return (
            <Card key={status.value} className="glass">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 rounded-lg ${status.color} text-white`}>
                      <StatusIcon className="h-4 w-4" />
                    </div>
                    <span className="text-sm font-medium">{status.label}</span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {statusInquiries.length}
                  </Badge>
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-3">
                {statusInquiries.map((inquiry) => (
                  <Card key={inquiry.id} className="card-hover border-border/50">
                    <CardContent className="p-3 sm:p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">
                              {inquiry.name}
                            </h4>
                            <p className="text-xs text-muted-foreground truncate">
                              {inquiry.email}
                            </p>
                          </div>
                          <div className="flex gap-1 shrink-0">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleEdit(inquiry)}
                              className="h-7 w-7 p-0 hover:bg-accent"
                              title="Edit inquiry"
                            >
                              <FileText className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleDelete(inquiry.id)}
                              className="h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                              title="Delete inquiry"
                            >
                              <XCircle className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        {inquiry.companyName && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Building className="h-3 w-3" />
                            <span className="truncate">{inquiry.companyName}</span>
                          </div>
                        )}

                        {inquiry.budget && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <DollarSign className="h-3 w-3" />
                            <span>{inquiry.budget}</span>
                          </div>
                        )}

                        {inquiry.timeline && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>{inquiry.timeline}</span>
                          </div>
                        )}

                        {inquiry.description && (
                          <p className="text-xs text-muted-foreground overflow-hidden">
                            {inquiry.description.length > 100
                              ? `${inquiry.description.substring(0, 100)}...`
                              : inquiry.description}
                          </p>
                        )}

                        <div className="flex items-center justify-between pt-2 border-t border-border/50">
                          <div className="text-xs text-muted-foreground">
                            {new Date(inquiry.created).toLocaleDateString()}
                          </div>

                          <Select
                            value={inquiry.status}
                            onValueChange={(newStatus) => handleStatusChange(inquiry.id, newStatus)}
                          >
                            <SelectTrigger className="h-6 w-auto text-xs border-none p-1">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {STATUSES.map((s) => (
                                <SelectItem key={s.value} value={s.value} className="text-xs">
                                  {s.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {statusInquiries.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <StatusIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No inquiries</p>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

    </div>
  );
}

'use client';
import React, { useEffect, useMemo, useState } from "react";

// Next.js single-file CRM Kanban board for Inquiries
// - Tailwind CSS required in the project
// - Drop this component into a Next.js page (app/page.tsx or pages/index.jsx)
// - No external drag-drop libs used (native HTML5 DnD)

export default function CRMKanban() {
  const STATUSES = [
    "Pending",
    "In Progress",
    "Deal",
    "Completed",
    "No deal",
  ];

  const sample = [
    {
      id: "inq-1",
      title: "Acme Corp — Website redesign",
      company: "Acme Corp",
      contact: "<EMAIL>",
      value: 4500,
      status: "Pending",
      createdAt: "2025-06-03",
    },
    {
      id: "inq-2",
      title: "Beta Ltd — Mobile app",
      company: "Beta Ltd",
      contact: "<EMAIL>",
      value: 12000,
      status: "In Progress",
      createdAt: "2025-07-01",
    },
    {
      id: "inq-3",
      title: "Gamma — Consulting",
      company: "Gamma",
      contact: "<EMAIL>",
      value: 3000,
      status: "Deal",
      createdAt: "2025-07-20",
    },
    {
      id: "inq-4",
      title: "Delta — Support retainer",
      company: "Delta",
      contact: "<EMAIL>",
      value: 2000,
      status: "No deal",
      createdAt: "2025-05-28",
    },
  ];

  const [cards, setCards] = useState(sample);
  const [query, setQuery] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [flashDealId, setFlashDealId] = useState(null);

  // filtered view
  const filtered = useMemo(() => {
    if (!query) return cards;
    const q = query.toLowerCase();
    return cards.filter(
      (c) =>
        c.title.toLowerCase().includes(q) ||
        c.company.toLowerCase().includes(q) ||
        c.contact.toLowerCase().includes(q)
    );
  }, [cards, query]);

  // Group by status
  const grouped = useMemo(() => {
    const map = {};
    STATUSES.forEach((s) => (map[s] = []));
    filtered.forEach((c) => map[c.status]?.push(c));
    return map;
  }, [filtered]);

  // Drag handlers
  function onDragStart(e, cardId) {
    e.dataTransfer.setData("text/plain", cardId);
    e.dataTransfer.effectAllowed = "move";
  }

  function onDrop(e, status) {
    e.preventDefault();
    const id = e.dataTransfer.getData("text/plain");
    setCards((prev) => {
      const found = prev.find((p) => p.id === id);
      if (!found) return prev;
      if (found.status === status) return prev; // no-op

      const updated = prev.map((p) =>
        p.id === id
          ? {
            ...p,
            status,
          }
          : p
      );

      // Trigger confetti if moved to Deal
      if (status === "Deal") {
        setFlashDealId(id);
        setShowConfetti(true);
        setTimeout(() => setShowConfetti(false), 4200);
      }

      return updated;
    });
  }

  function onDragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  }

  // Small UI helpers
  function addNewInquiry() {
    const id = `inq-${Math.random().toString(36).slice(2, 9)}`;
    const newCard = {
      id,
      title: `New Lead ${id}`,
      company: "—",
      contact: "—",
      value: 0,
      status: "Pending",
      createdAt: new Date().toISOString().slice(0, 10),
    };
    setCards((c) => [newCard, ...c]);
  }

  function updateValue(id, delta) {
    setCards((prev) =>
      prev.map((p) => (p.id === id ? { ...p, value: Math.max(0, p.value + delta) } : p))
    );
  }

  // Column totals
  function columnTotal(status) {
    return cards
      .filter((c) => c.status === status)
      .reduce((s, c) => s + (Number(c.value) || 0), 0);
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6 text-slate-100">
      <div className="max-w-[1400px] mx-auto">
        <header className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-extrabold tracking-tight flex items-center gap-3">
              <span className="inline-block w-3 h-3 rounded-full bg-gradient-to-r from-emerald-400 to-cyan-300 animate-pulse shadow-lg" />
              Inquiries CRM — Kanban
            </h1>
            <p className="text-slate-300 text-sm mt-1">Drag & drop — shiny tailwind effects ✨</p>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search title, company, contact..."
                className="px-3 py-2 rounded-md bg-slate-700 placeholder-slate-400 outline-none focus:ring-2 focus:ring-cyan-400 transition-shadow shadow-sm"
              />
              {query && (
                <button
                  onClick={() => setQuery("")}
                  className="absolute right-1 top-1/2 -translate-y-1/2 text-xs px-2 py-1 bg-slate-600 rounded-md hover:bg-slate-500"
                >
                  Clear
                </button>
              )}
            </div>

            <button
              onClick={addNewInquiry}
              className="px-4 py-2 rounded-lg bg-gradient-to-r from-cyan-400 to-emerald-400 text-slate-900 font-semibold shadow-2xl hover:scale-105 transform transition"
            >
              + New Inquiry
            </button>
          </div>
        </header>

        <main>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {STATUSES.map((status) => (
              <section key={status} className="flex flex-col">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-lg font-semibold flex items-center gap-2">
                    <span className="inline-flex items-center justify-center w-9 h-9 rounded-lg bg-slate-700/30 backdrop-blur-sm border border-slate-700">
                      {status[0]}
                    </span>
                    {status}
                  </h2>
                  <div className="text-sm text-slate-300">${columnTotal(status)}</div>
                </div>

                <div
                  onDragOver={onDragOver}
                  onDrop={(e) => onDrop(e, status)}
                  className="min-h-[120px] p-2 rounded-xl bg-slate-800/40 border border-slate-700/60 backdrop-blur-sm transition-shadow hover:shadow-[0_8px_30px_rgba(34,197,94,0.06)]"
                >
                  {/* Progress bar for the column */}
                  <div className="mb-2">
                    <ColumnProgress cards={grouped[status]} />
                  </div>

                  <div className="space-y-3">
                    {grouped[status].map((card) => (
                      <article
                        key={card.id}
                        draggable
                        onDragStart={(e) => onDragStart(e, card.id)}
                        className={`relative p-3 rounded-xl bg-gradient-to-br from-slate-900/80 to-slate-800/60 border border-slate-700/50 shadow-md transform transition hover:-translate-y-1 hover:scale-[1.008] cursor-grab`}
                      >
                        <div className="flex justify-between items-start gap-2">
                          <div className="flex-1">
                            <h3 className="font-semibold text-sm truncate">{card.title}</h3>
                            <p className="text-xs text-slate-400 mt-1 truncate">{card.company} • {card.contact}</p>
                          </div>

                          <div className="flex flex-col items-end gap-2">
                            <div className="text-sm font-semibold">${card.value}</div>
                            <div className="text-xs text-slate-400">{card.createdAt}</div>
                          </div>
                        </div>

                        <div className="mt-3 flex items-center justify-between gap-2">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => updateValue(card.id, 100)}
                              className="text-xs px-2 py-1 rounded bg-slate-700/50 hover:bg-slate-700 text-slate-200"
                            >
                              +100
                            </button>
                            <button
                              onClick={() => updateValue(card.id, -100)}
                              className="text-xs px-2 py-1 rounded bg-slate-700/50 hover:bg-slate-700 text-slate-200"
                            >
                              -100
                            </button>

                            <span className="text-xs text-slate-400 ml-2">ID: {card.id}</span>
                          </div>

                          <div className="flex items-center gap-2">
                            <small className="px-2 py-1 rounded text-xs bg-slate-700/30">{card.status}</small>
                          </div>
                        </div>

                        {/* flash glow when card moved to Deal */}
                        {flashDealId === card.id && showConfetti && (
                          <div className="absolute inset-0 pointer-events-none animate-deal-flash rounded-xl" />
                        )}
                      </article>
                    ))}

                    {grouped[status].length === 0 && (
                      <div className="text-xs text-slate-400 italic p-3">No inquiries</div>
                    )}
                  </div>
                </div>
              </section>
            ))}
          </div>
        </main>

        {/* Confetti overlay */}
        {showConfetti && <Confetti />}

      </div>

      <style jsx>{`
        @keyframes float1 {
          0% { transform: translateY(0) rotate(0deg); opacity: 1 }
          100% { transform: translateY(420px) rotate(720deg); opacity: 0 }
        }

        .animate-deal-flash {
          box-shadow: 0 0 40px 6px rgba(56, 189, 248, 0.18), inset 0 0 40px 6px rgba(34,197,94,0.06);
          animation: pulse 1.2s ease-in-out 3;
        }

        @keyframes pulse {
          0% { transform: scale(1) }
          50% { transform: scale(1.02) }
          100% { transform: scale(1) }
        }
      `}</style>
    </div>
  );
}

function ColumnProgress({ cards = [] }) {
  const total = cards.length || 0;
  const valueTotal = cards.reduce((s, c) => s + (Number(c.value) || 0), 0);
  return (
    <div className="flex items-center gap-3">
      <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
        <div
          style={{ width: `${Math.min(100, total * 8)}%` }}
          className="h-2 bg-gradient-to-r from-cyan-400 to-emerald-400 transition-all"
        />
      </div>
      <div className="text-xs text-slate-300 whitespace-nowrap">{total} • ${valueTotal}</div>
    </div>
  );
}

function Confetti() {
  // lightweight CSS-only confetti using absolutely positioned spans
  const pieces = Array.from({ length: 28 }).map((_, i) => {
    const left = Math.random() * 100;
    const delay = Math.random() * 0.6;
    const duration = 2 + Math.random() * 2.4;
    const size = 8 + Math.random() * 12;
    const rotate = Math.random() * 360;
    const colors = ["#34D399", "#06B6D4", "#F59E0B", "#FB7185", "#A78BFA"];
    const color = colors[Math.floor(Math.random() * colors.length)];
    return { id: i, left, delay, duration, size, rotate, color };
  });

  return (
    <div className="pointer-events-none fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0">
        {pieces.map((p) => (
          <span
            key={p.id}
            style={{
              left: `${p.left}%`,
              top: `-8%`,
              width: `${p.size}px`,
              height: `${p.size * 0.6}px`,
              background: p.color,
              transform: `rotate(${p.rotate}deg)`,
              animationDelay: `${p.delay}s`,
              animationDuration: `${p.duration}s`,
            }}
            className={`absolute rounded-sm block animate-confetti`}
          />
        ))}
      </div>

      <style jsx>{`
        @keyframes confetti-fall {
          0% { transform: translateY(-8vh) rotate(0deg); opacity: 1 }
          100% { transform: translateY(110vh) rotate(540deg); opacity: 0 }
        }

        .animate-confetti {
          animation-name: confetti-fall;
          animation-timing-function: cubic-bezier(.2,.8,.3,1);
          will-change: transform, opacity;
        }
      `}</style>
    </div>
  );
}

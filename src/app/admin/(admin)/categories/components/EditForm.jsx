import { useState } from "react";
import { Upload, Pencil } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useCollection } from "@/hooks/useCollection";
import { toast } from "sonner";
import { createGeneralAuditLog } from "@/utils/auditLogger";
import { AUDIT_ACTIONS } from "@/constants/audit";
import { DynamicMultiDatalist } from "@/components/ui/data-multi-list";

export default function EditForm(
  {
    info = { id: '', title: '', description: '', slug: '', tags: [] }
  }
) {

  const { updateItem, mutation } = useCollection('categories');
  const { data: listTags } = useCollection('tags');
  const [tags, setTags] = useState(info?.expand?.tags)
  const [formData, setFormData] = useState(info);
  const [isOpen, setIsOpen] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleReset = () => {
    setFormData({
      title: '',
      description: '',
      slug: ''
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const updatedService = await updateItem(formData.id, {
        title: formData.title,
        description: formData.description,
        slug: formData.slug
      });
      await createGeneralAuditLog({
        action: AUDIT_ACTIONS.EDIT,
        module: `Tags`,
        details: updatedService
      });
      toast.success('Updated the tag');
    } catch (error) {
      console.log(error)
      toast.error(error.message);
    } finally {
      handleReset();
      mutation();
      setIsOpen(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={setIsOpen}
      className='bg-accent w-full cursor-pointer max-w-2xl'
    >
      <DialogTrigger asChild>
        <button
          className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
        >
          <p>Edit Details</p>
          <Pencil
            size={18}
            className="cursor-pointer"
          />
        </button>
      </DialogTrigger>
      <DialogContent className={'bg-accent max-w-2xl'}>
        <DialogHeader>
          <DialogTitle>Update Tag</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 p-4">
          <div className="flex flex-col gap-2">
            <Label>Title</Label>
            <Input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="e.g., Artificial Intelligence"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Slug</Label>
            <Input
              type="text"
              name="price"
              value={formData.slug}
              onChange={handleChange}
              placeholder="e.g., artificial-intelligence"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Description</Label>
            <Textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Short description"
              className="bg-accent"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label>Tags</Label>
            <DynamicMultiDatalist
              items={listTags || []}
              existing={tags || []}
              label="Tags"
              getLabel={(c) => c?.title}
              placeholder="Select tag(s)..."
              onChange={(selected) => setTags(selected.map((c) => c.id))}
            />
          </div>

          <div className="mt-6">
            <Button
              onClick={handleSubmit}
            >
              <p>Update Tag</p>
              <Upload className='w-5 h-5' />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
};

import { DataTable } from '@/components/ui/data-table';
import { useCollection } from '@/hooks/useCollection'
import React from 'react'
import Form from './Form';
import EditForm from './EditForm';
import MobileDataTable from '@/components/ui/mobile-data-table';
import { useIsMobile } from '@/hooks/use-mobile';
import DetailsActions from '@/components/actions-buttons/DetailsActions';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';
import { Badge } from '@/components/ui/badge';
import pbclient from '@/lib/db';

export default function CategoriesLayout() {
  const { data, deleteItem } = useCollection('categories', {
    expand: 'tags',
  });

  const columns = [
    {
      id: 'id',
      accessorKey: 'id',
      header: 'Category ID',
      filterable: true,
      cell: ({ row }) => <div>{row.original.id}</div>,
    },
    {
      id: 'icon',
      accessorKey: 'icon',
      header: 'Icon',
      filterable: true,
      cell: ({ row }) => {
        const icon = row.original?.icon;
        if (!icon) return null;
        const url = pbclient.files.getURL(row.original, icon);
        return (
          <LazyLoadingImage src={url} alt={row.original?.title || 'icon'} className="w-10 h-10 rounded-md object-cover" />
        );
      }
    },
    {
      id: 'background',
      accessorKey: 'background',
      header: 'Background',
      filterable: true,
      cell: ({ row }) => {
        const icon = row.original?.background;
        if (!icon) return null;
        const url = pbclient.files.getURL(row.original, icon);
        return (
          <LazyLoadingImage src={url} alt={row.original?.title || 'background'} className="w-20 rounded-md object-cover" />
        );
      }
    },
    {
      id: 'title',
      accessorKey: 'title',
      header: 'Title',
      filterable: true,
      cell: ({ row }) => <div>{row.original?.title}</div>,
    },
    {
      id: 'description',
      accessorKey: 'description',
      header: 'Description',
      filterable: true,
      cell: ({ row }) => <div className='w-[150px] whitespace-pre-line'>{row.original.description}</div>,
    },
    {
      id: 'slug',
      accessorKey: 'slug',
      header: 'Slug',
      filterable: true,
      cell: ({ row }) => <div>{row.original.slug}</div>,
    },
    {
      id: 'tags',
      accessorKey: 'tags',
      header: 'Tags',
      filterable: true,
      cell: ({ row }) => {
        return (
          <div className={`grid gap-4 p-2`}>
            {row.original?.expand?.tags?.map((tag, index) => (
              <Badge variant={'outline'} key={index}>
                {tag?.title}
              </Badge>
            ))}
          </div>
        )
      }
    },
    {
      id: 'actions',
      accessorKey: 'actions',
      header: 'Actions',
      filterable: false,
      cell: ({ row }) => (
        <DetailsActions
          row={row}
          EditForm={EditForm}
          deleteItem={deleteItem}
          showEye={false}
        />
      ),
    }
  ];

  return (
    <>
      {
        useIsMobile() ? (
          <div className="border-2 bg-background md:p-4 rounded-xl mt-8">
            <h1 className="text-xl font-semibold p-4">Categories</h1>
            <div className="flex justify-end p-4">
              <Form />
            </div>
            <MobileDataTable
              columns={columns}
              data={data}
            />
          </div>
        ) : (
          <div className="border-2 bg-background dark:bg-accent md:p-4 rounded-xl mt-8">
            <div className="flex items-center justify-between gap-4">
              <h1 className="text-lg font-semibold">Categories</h1>
              <Form />
            </div>

            <DataTable
              columns={columns}
              data={data}
            />
          </div>
        )
      }
    </>
  )
};

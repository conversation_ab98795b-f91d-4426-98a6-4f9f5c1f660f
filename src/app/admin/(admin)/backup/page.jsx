'use client';
import React, { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import {
  Database,
  Download,
  Upload,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  HardDrive,
  FileText,
  Trash2,
  Calendar,
  Shield,
  Zap,
  Archive,
  CloudDownload,
  History,
  Settings
} from "lucide-react";

export default function BackupPage() {
  const [backupHistory, setBackupHistory] = useState([
    {
      id: 1,
      name: "backup-2024-01-15.json",
      date: "2024-01-15T10:30:00Z",
      size: "2.4 MB",
      type: "manual",
      status: "completed"
    },
    {
      id: 2,
      name: "backup-2024-01-14.json",
      date: "2024-01-14T02:00:00Z",
      size: "2.3 MB",
      type: "automatic",
      status: "completed"
    },
    {
      id: 3,
      name: "backup-2024-01-13.json",
      date: "2024-01-13T02:00:00Z",
      size: "2.2 MB",
      type: "automatic",
      status: "completed"
    }
  ]);

  const [settings, setSettings] = useState({
    autoBackup: true,
    frequency: "daily",
    retentionDays: "30",
    includeMedia: true,
    includeDatabase: true,
    includeSettings: true,
    compression: true
  });

  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);
  const [restoreProgress, setRestoreProgress] = useState(0);

  // Storage statistics
  const storageStats = useMemo(() => {
    return {
      total: "10 GB",
      used: "3.2 GB",
      percentage: 32,
      breakdown: {
        posts: { size: "1.8 GB", percentage: 56 },
        media: { size: "1.2 GB", percentage: 38 },
        database: { size: "0.2 GB", percentage: 6 }
      }
    };
  }, []);

  // Handle backup creation
  const handleCreateBackup = async () => {
    setIsBackingUp(true);
    setBackupProgress(0);

    try {
      // Simulate backup process with progress
      const steps = [
        { name: "Preparing backup...", duration: 500 },
        { name: "Backing up database...", duration: 1000 },
        { name: "Backing up posts...", duration: 1500 },
        { name: "Backing up media...", duration: 1000 },
        { name: "Compressing files...", duration: 800 },
        { name: "Finalizing backup...", duration: 200 }
      ];

      let progress = 0;
      for (const step of steps) {
        toast.info(step.name);
        await new Promise(resolve => setTimeout(resolve, step.duration));
        progress += 100 / steps.length;
        setBackupProgress(Math.round(progress));
      }

      // Create backup data
      const backupData = {
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        settings,
        posts: "Sample posts data",
        users: "Sample users data",
        categories: "Sample categories data",
        media: settings.includeMedia ? "Sample media data" : null
      };

      // Download backup file
      const blob = new Blob([JSON.stringify(backupData, null, 2)], { 
        type: 'application/json' 
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      // Add to backup history
      const newBackup = {
        id: Date.now(),
        name: `backup-${new Date().toISOString().split('T')[0]}.json`,
        date: new Date().toISOString(),
        size: "2.6 MB",
        type: "manual",
        status: "completed"
      };
      setBackupHistory(prev => [newBackup, ...prev]);

      toast.success("Backup created and downloaded successfully!");
    } catch (error) {
      toast.error("Failed to create backup");
      console.error(error);
    } finally {
      setIsBackingUp(false);
      setBackupProgress(0);
    }
  };

  // Handle restore
  const handleRestore = async (file) => {
    if (!file) return;

    setIsRestoring(true);
    setRestoreProgress(0);

    try {
      const text = await file.text();
      const backupData = JSON.parse(text);
      
      // Simulate restore process with progress
      const steps = [
        { name: "Validating backup file...", duration: 500 },
        { name: "Restoring database...", duration: 1200 },
        { name: "Restoring posts...", duration: 1500 },
        { name: "Restoring settings...", duration: 800 },
        { name: "Rebuilding indexes...", duration: 1000 },
        { name: "Finalizing restore...", duration: 300 }
      ];

      let progress = 0;
      for (const step of steps) {
        toast.info(step.name);
        await new Promise(resolve => setTimeout(resolve, step.duration));
        progress += 100 / steps.length;
        setRestoreProgress(Math.round(progress));
      }

      if (backupData.settings) {
        setSettings(backupData.settings);
      }

      toast.success("Data restored successfully!");
    } catch (error) {
      toast.error("Failed to restore data. Invalid backup file.");
      console.error(error);
    } finally {
      setIsRestoring(false);
      setRestoreProgress(0);
    }
  };

  // Handle delete backup
  const handleDeleteBackup = (backupId) => {
    if (confirm("Are you sure you want to delete this backup?")) {
      setBackupHistory(prev => prev.filter(backup => backup.id !== backupId));
      toast.success("Backup deleted successfully");
    }
  };

  // Handle settings change
  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-headline flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              <Database className="h-6 w-6" />
            </div>
            Backup & Restore
          </h1>
          <p className="text-muted-foreground">
            Manage your data backups and restore points for data protection.
          </p>
        </div>
        
        <div className="flex gap-3">
          <Button 
            onClick={handleCreateBackup}
            disabled={isBackingUp || isRestoring}
            className="btn-modern"
          >
            {isBackingUp ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            {isBackingUp ? "Creating..." : "Create Backup"}
          </Button>
        </div>
      </div>

      {/* Progress Indicators */}
      {(isBackingUp || isRestoring) && (
        <Card className="glass border-primary/20">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {isBackingUp ? "Creating Backup" : "Restoring Data"}
                </span>
                <span className="text-sm text-muted-foreground">
                  {isBackingUp ? backupProgress : restoreProgress}%
                </span>
              </div>
              <Progress 
                value={isBackingUp ? backupProgress : restoreProgress} 
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Storage Overview */}
      <Card className="glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Storage Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Storage Used</span>
              <span className="text-sm text-muted-foreground">
                {storageStats.used} of {storageStats.total}
              </span>
            </div>
            
            <div className="w-full bg-muted rounded-full h-3">
              <div 
                className="bg-gradient-to-r from-primary to-primary/80 h-3 rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${storageStats.percentage}%` }}
              />
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              {Object.entries(storageStats.breakdown).map(([key, data]) => (
                <div key={key} className="text-center space-y-2">
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary/60 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${data.percentage}%` }}
                    />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{data.size}</div>
                    <div className="text-xs text-muted-foreground capitalize">{key}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Backup Settings */}
        <Card className="glass">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Backup Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Automatic Backups</Label>
                <p className="text-sm text-muted-foreground">
                  Create backups automatically on schedule
                </p>
              </div>
              <Switch
                checked={settings.autoBackup}
                onCheckedChange={(checked) => handleSettingChange('autoBackup', checked)}
              />
            </div>

            {settings.autoBackup && (
              <div className="space-y-4 pl-6 border-l-2 border-primary/20">
                <div className="space-y-2">
                  <Label htmlFor="frequency">Backup Frequency</Label>
                  <select
                    id="frequency"
                    value={settings.frequency}
                    onChange={(e) => handleSettingChange('frequency', e.target.value)}
                    className="w-full h-9 px-3 rounded-md border border-input bg-background text-sm"
                  >
                    <option value="hourly">Every Hour</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="retention">Retention Period (days)</Label>
                  <Input
                    id="retention"
                    type="number"
                    value={settings.retentionDays}
                    onChange={(e) => handleSettingChange('retentionDays', e.target.value)}
                    placeholder="30"
                  />
                </div>
              </div>
            )}

            <div className="space-y-4">
              <Label>Backup Content</Label>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Include Database</span>
                  <Switch
                    checked={settings.includeDatabase}
                    onCheckedChange={(checked) => handleSettingChange('includeDatabase', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Include Media Files</span>
                  <Switch
                    checked={settings.includeMedia}
                    onCheckedChange={(checked) => handleSettingChange('includeMedia', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Include Settings</span>
                  <Switch
                    checked={settings.includeSettings}
                    onCheckedChange={(checked) => handleSettingChange('includeSettings', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Enable Compression</span>
                  <Switch
                    checked={settings.compression}
                    onCheckedChange={(checked) => handleSettingChange('compression', checked)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Restore Data */}
        <Card className="glass">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Restore Data
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                <CloudDownload className="h-8 w-8 text-muted-foreground" />
              </div>

              <div>
                <h3 className="font-medium">Upload Backup File</h3>
                <p className="text-sm text-muted-foreground">
                  Select a backup file to restore your data
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <input
                type="file"
                accept=".json"
                onChange={(e) => handleRestore(e.target.files[0])}
                className="hidden"
                id="restore-file"
                disabled={isRestoring || isBackingUp}
              />

              <Button
                onClick={() => document.getElementById('restore-file').click()}
                disabled={isRestoring || isBackingUp}
                variant="outline"
                className="w-full"
                size="lg"
              >
                {isRestoring ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    Restoring Data...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Choose Backup File
                  </>
                )}
              </Button>

              <div className="flex items-start gap-3 p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    Important Warning
                  </p>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300">
                    Restoring will overwrite all current data. Make sure to create a backup before proceeding.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Backup History */}
      <Card className="glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Backup History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {backupHistory.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Archive className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No backups found</p>
                <p className="text-sm">Create your first backup to get started</p>
              </div>
            ) : (
              <div className="space-y-3">
                {backupHistory.map((backup) => (
                  <div
                    key={backup.id}
                    className="flex items-center justify-between p-4 rounded-lg border border-border/50 hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <FileText className="h-4 w-4 text-primary" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{backup.name}</span>
                          <Badge
                            variant={backup.type === 'automatic' ? 'secondary' : 'default'}
                            className="text-xs"
                          >
                            {backup.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            <CheckCircle className="h-2 w-2 mr-1" />
                            {backup.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(backup.date).toLocaleString()}
                          </span>
                          <span className="flex items-center gap-1">
                            <HardDrive className="h-3 w-3" />
                            {backup.size}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          // Simulate download
                          toast.success("Backup download started");
                        }}
                        className="h-8"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteBackup(backup.id)}
                        className="h-8 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="glass card-hover cursor-pointer" onClick={handleCreateBackup}>
          <CardContent className="p-6 text-center space-y-3">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Download className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-medium">Quick Backup</h3>
              <p className="text-sm text-muted-foreground">Create an instant backup</p>
            </div>
          </CardContent>
        </Card>

        <Card className="glass card-hover cursor-pointer">
          <CardContent className="p-6 text-center space-y-3">
            <div className="mx-auto w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center">
              <Shield className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-medium">Auto Backup</h3>
              <p className="text-sm text-muted-foreground">
                {settings.autoBackup ? 'Enabled' : 'Disabled'}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="glass card-hover cursor-pointer">
          <CardContent className="p-6 text-center space-y-3">
            <div className="mx-auto w-12 h-12 bg-blue-500/10 rounded-full flex items-center justify-center">
              <Zap className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium">Last Backup</h3>
              <p className="text-sm text-muted-foreground">
                {backupHistory.length > 0
                  ? new Date(backupHistory[0].date).toLocaleDateString()
                  : 'Never'
                }
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

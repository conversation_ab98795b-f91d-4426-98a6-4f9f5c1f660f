'use client'

import { useMemo } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useCollection } from '@/hooks/useCollection'

function StatCard({ title, value, extra }) {
  return (
    <Card className="bg-muted/30 border-dashed">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm text-muted-foreground">{title}</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="text-3xl font-semibold tracking-tight">{value}</div>
        {extra && <div className="text-xs text-muted-foreground mt-1">{extra}</div>}
      </CardContent>
    </Card>
  )
}

export default function DashboardPage() {
  // Fetch live data from PocketBase collections; fall back to 0/[] if missing
  const { data: posts } = useCollection('posts', { sort: '-created', perPage: 10 })
  const { data: categories } = useCollection('categories')
  const { data: tags } = useCollection('tags')
  const { data: authors } = useCollection('user_profiles')

  const totals = useMemo(() => ({
    posts: posts?.length || 0,
    categories: categories?.length || 0,
    tags: tags?.length || 0,
    authors: authors?.length || 0,
  }), [posts, categories, tags, authors])

  const recentPosts = useMemo(() => (posts || []).slice(0, 6), [posts])

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard title="Total Articles" value={totals.posts} extra="Published" />
        <StatCard title="Categories" value={totals.categories} extra="Active" />
        <StatCard title="Tags" value={totals.tags} extra="Across all posts" />
        <StatCard title="Authors" value={totals.authors} extra="Contributors" />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="bg-muted/20">
          <CardHeader>
            <CardTitle>Recent Articles</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentPosts.map((post) => (
                <div key={post.id} className="flex items-center justify-between">
                  <div className="min-w-0">
                    <Link href={`/blog/${post.slug || post.id}`} className="font-medium hover:underline truncate block">
                      {post.title || `Post ${post.id}`}
                    </Link>
                    <div className="text-xs text-muted-foreground truncate">
                      {post.category || 'Uncategorized'}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {(post.tags || []).slice(0, 2).map((t) => (
                      <Badge key={String(t)} variant="secondary" className="whitespace-nowrap">{String(t)}</Badge>
                    ))}
                  </div>
                </div>
              ))}
              {recentPosts.length === 0 && (
                <div className="text-sm text-muted-foreground">No posts yet.</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-muted/20">
          <CardHeader>
            <CardTitle>Quick Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <Link className="p-3 rounded-md bg-background border hover:bg-accent transition" href="/admin/posts">Manage Blogs</Link>
              <Link className="p-3 rounded-md bg-background border hover:bg-accent transition" href="/admin/backup">Data Backups</Link>
              <Link className="p-3 rounded-md bg-background border hover:bg-accent transition" href="/admin/settings">Settings</Link>
              <Link className="p-3 rounded-md bg-background border hover:bg-accent transition" href="/admin/profile">Profile</Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

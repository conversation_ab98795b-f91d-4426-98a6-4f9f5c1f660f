import { useState, useEffect, useCallback, useMemo } from 'react';
import { User } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import pbclient from '@/lib/db';
import { toast } from 'sonner';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ROLES_ARRAY } from '@/constants/perms';
import { Textarea } from '@/components/ui/textarea';

export default function ProfileOverview({ user, userProfile, onUserUpdate, refreshProfile, loading }) {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    // User fields - Initialize with empty strings to prevent controlled/uncontrolled warnings
    firstName: '',
    lastName: '',
    email: '',
    bio: '',
    longBio: '',
    role: '',
  });

  // Memoize form data initialization to prevent unnecessary re-renders
  const initialFormData = useMemo(() => {
    const baseData = {
      firstName: '',
      lastName: '',
      email: '',
      bio: '',
      longBio: '',
      role: '',
    };

    if (userProfile) {
      return {
        ...baseData,
        firstName: userProfile?.firstName || '',
        lastName: userProfile?.lastName || '',
        bio: userProfile?.bio || '',
        longBio: userProfile?.longBio || '',
        role: userProfile?.role || '',
      };
    }

    if (user) {
      return {
        ...baseData,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
      };
    }

    return baseData;
  }, [user, userProfile]);
  console.log(initialFormData);

  useEffect(() => {
    setFormData(initialFormData);
  }, [initialFormData]);

  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  }, []);

  const handleSave = useCallback(async () => {
    setIsLoading(true);
    try {
      // Update or create profile data
      const profileData = {
        user: user.id,
        firstName: formData.firstName,
        lastName: formData.lastName,
        bio: formData.bio,
        role: formData.role,
        longBio: formData.longBio,
        expertise: formData.expertise,
        specialisation: formData.specialisation,
        contact: formData.contact
      };

      let updatedUser;
      if (userProfile) {
        updatedUser = await pbclient.collection('user_profiles').update(userProfile.id, profileData);
      } else {
        updatedUser = await pbclient.collection('user_profiles').create(profileData);
      }

      if (onUserUpdate) {
        onUserUpdate(updatedUser);
      }

      refreshProfile();
      setIsEditing(false);
      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, formData, userProfile, onUserUpdate, refreshProfile]);

  const formatDate = useCallback((dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  }, []);

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-card to-card/80 rounded-2xl shadow-lg p-6 backdrop-blur-sm">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded w-1/4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-card to-card/80 rounded-2xl shadow-lg p-6 backdrop-blur-sm">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <User className="w-5 h-5 text-primary" />
          <h2 className="text-xl font-semibold">Profile Overview</h2>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button
                onClick={() => setIsEditing(false)}
                disabled={isLoading}
                variant={'outline'}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={isLoading}
              >
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
            >
              Edit Info
            </Button>
          )}
        </div>
      </div>

      {/* Profile Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Full Name */}
        <div className="space-y-2">
          <Label title="Full Name" className={'p-1'}>
            First Name:
          </Label>
          <Input
            type="text"
            name="firstName"
            placeholder="First Name"
            disabled={!isEditing}
            value={formData.firstName}
            onChange={handleInputChange}
            className="md:text-base text-xs"
          />
        </div>

        {/* Email */}
        <div className="space-y-2">
          <Label title="Last Name" className={'p-1'}>
            Last Name:
          </Label>
          <Input
            type="text"
            name="lastName"
            placeholder="Last Name"
            disabled={!isEditing}
            value={formData.lastName}
            onChange={handleInputChange}
            className="md:text-base text-xs"
          />
        </div>

        {/* Bio */}
        <div className="space-y-2">
          <Label title="bio" className="p-1">
            Bio:
          </Label>
          <Textarea
            name="bio"
            placeholder="Small Bio"
            disabled={!isEditing}
            value={formData.bio}
            onChange={handleInputChange}
            className="md:text-base text-xs"
          />
        </div>

        <div className="space-y-2">
          <Label title="Long Bio" className="p-1">
            Long Bio:
          </Label>
          <Textarea
            name="longBio"
            value={formData.longBio}
            placeholder="Long Bio"
            disabled={!isEditing}
            onChange={handleInputChange}
            className="md:text-base text-xs"
          />
        </div>

        {/* role */}
        <div className="space-y-2">
          <Label title="role" className="p-1">
            Role:
          </Label>
          <Select
            name="role"
            value={formData.role}
            disabled={!isEditing}
            onChange={(value) => setFormData({ ...formData, role: value })}
          >
            <SelectTrigger className="md:text-base text-xs w-full">
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Roles</SelectLabel>
                {ROLES_ARRAY?.map((role, index) => (
                  <SelectItem
                    key={index}
                    value={role}
                  >
                    {role}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        {/* Joined On */}
        <div className="space-y-2">
          <Label title="Joined On" className="p-1">
            Joined On:
          </Label>
          <Input
            type="text"
            disabled={true}
            value={formatDate(user?.created) || ''}
            onChange={() => { }}
            className="md:text-base text-xs"
          />
        </div>
      </div>
    </div>
  );
}

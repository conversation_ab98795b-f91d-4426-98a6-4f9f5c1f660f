'use client'
import { useState, useMemo, useEffect, useCallback } from 'react';
import { Pencil, RefreshCw } from 'lucide-react';
import pbclient from '@/lib/db';
import { toast } from 'sonner';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { AvatarFallback } from '@radix-ui/react-avatar';

export default function ProfileHeader({ user, onUserUpdate, userProfile }) {
  const [isUploading, setIsUploading] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [profileInfo, setProfileInfo] = useState({
    url: '',
    initials: '',
    progressColor: '#6B7280', // Default gray color
  });

  // Ensure client-side rendering for hydration consistency
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Calculate profile completion percentage and missing fields
  const { profileCompletion, missingFields, progressColor } = useMemo(() => {
    // Return default values if not on client or no user data
    if (!isClient || !user) {
      return {
        profileCompletion: 0,
        missingFields: [],
        progressColor: '#6B7280' // Default gray
      };
    }

    const requiredFields = [
      { field: 'firstName', label: 'First Name', weight: 7, value: userProfile?.firstName },
      { field: 'lastName', label: 'Last Name', weight: 7, value: userProfile?.lastName },
      { field: 'avatar', label: 'Profile Picture', weight: 9, value: user.avatar },
      { field: 'bio', label: 'Bio', weight: 7, value: userProfile?.bio },
      { field: 'longBio', label: 'Long Bio', weight: 7, value: userProfile?.longBio },
      { field: 'role', label: 'Role', weight: 7, value: userProfile?.role },
      { field: 'expertise', label: 'Expertise', weight: 7, value: userProfile?.expertise },
      { field: 'specialisation', label: 'Specialisation', weight: 7, value: userProfile?.specialisation },
      { field: 'socials', label: 'Socials', weight: 7, value: userProfile?.socials },
      { field: 'achievements', label: 'Achievements', weight: 7, value: userProfile?.achievements },
      { field: 'languages', label: 'Languages', weight: 7, value: userProfile?.languages },
      { field: 'education', label: 'Education', weight: 7, value: userProfile?.education },
      { field: 'experience', label: 'Experience', weight: 7, value: userProfile?.experience },
    ];

    const completedWeight = requiredFields.reduce((total, field) => {
      return total + (field.value ? field.weight : 0);
    }, 0);

    const missing = requiredFields.filter(field => !field.value).map(field => field.label);
    const completion = Math.round(completedWeight);

    // Calculate progress color based on completion percentage
    const getProgressColor = (completion) => {
      if (completion >= 80) return '#10B981'; // Green
      if (completion >= 60) return '#F59E0B'; // Yellow
      if (completion >= 40) return '#EF4444'; // Red
      return '#6B7280'; // Gray
    };

    return {
      profileCompletion: completion,
      missingFields: missing,
      progressColor: getProgressColor(completion)
    };
  }, [isClient, user, userProfile]);


  useEffect(() => {
    if (isClient && user) {
      const getAvatarUrl = () => {
        if (user?.avatar) {
          return pbclient.files.getURL(user, user.avatar);
        }
        return null;
      };

      const getInitials = () => {
        if (user?.firstname && user?.lastname) {
          return `${user.firstname.charAt(0)}${user.lastname.charAt(0)}`.toUpperCase();
        }
        if (user?.name) {
          const names = user.name.split(' ');
          return names.length > 1
            ? `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`.toUpperCase()
            : names[0].charAt(0).toUpperCase();
        }
        return user?.email?.charAt(0).toUpperCase() || 'U';
      };

      const url = getAvatarUrl();
      const initials = getInitials();

      setProfileInfo(prev => ({
        ...prev,
        url,
        initials
      }));
    }
  }, [isClient, user])


  const handleAvatarUpload = useCallback(async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const updatedUser = await pbclient.collection('users').update(user.id, formData);

      if (onUserUpdate) {
        onUserUpdate(updatedUser);
      }

      toast.success('Profile picture updated successfully');
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Failed to upload profile picture');
    } finally {
      setIsUploading(false);
    }
  }, [user?.id, onUserUpdate]);

  return (
    <div className="bg-background dark:bg-secondary rounded-2xl shadow-lg p-6">
      <div className="flex flex-col items-center justify-center space-y-4">
        <div className="relative">

          {/* Avatar Circle */}
          <Avatar className="w-32 h-32 rounded-full flex items-center justify-center shadow-lg overflow-hidden">
            <AvatarImage src={profileInfo?.url} alt={"Profile"} />
            <AvatarFallback>{profileInfo?.initials}</AvatarFallback>
          </Avatar>

          {/* Camera Icon for Upload */}
          <label
            htmlFor="avatar-upload"
            className="mt-2 bg-primary text-white rounded-full p-2 shadow-md cursor-pointer flex items-center justify-center"
          >
            {isUploading ? (
              <RefreshCw size={18} className="animate-spin" />
            ) : (
              <Pencil size={18} />
            )}
          </label>
          <div className="absolute bottom-1 right-1">
            <input
              id="avatar-upload"
              type="file"
              accept="image/*"
              onChange={handleAvatarUpload}
              className="hidden"
              disabled={isUploading}
            />
          </div>
        </div>

        {/* Profile Completion Status */}
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">Profile Completion</h3>
          <div className="flex items-center justify-center space-x-2">
            <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full transition-all duration-500 ease-in-out rounded-full"
                style={{
                  width: `${profileCompletion}%`,
                  backgroundColor: progressColor
                }}
              />
            </div>
            <span className="text-sm font-medium text-secondary-foreground/60">
              {profileCompletion}% Complete
            </span>
          </div>

          {/* Completion Message */}
          <p className="text-sm text-secondary-foreground/60">
            {profileCompletion === 100
              ? "🎉 Your profile is complete!"
              : profileCompletion >= 80
                ? "Almost there! Just a few more details needed."
                : profileCompletion >= 60
                  ? "Good progress! Keep adding your information."
                  : profileCompletion >= 40
                    ? "Getting started! Please complete your profile."
                    : "Let's get your profile set up!"
            }
          </p>

          {/* Missing Fields Indicator */}
          {profileCompletion < 100 && missingFields.length > 0 && (
            <div className="mt-3 p-3 bg-primary/10 border border-primary/10 rounded-lg">
              <p className="text-xs font-medium text-primary mb-1">Missing Information:</p>
              <div className="flex flex-wrap gap-1">
                {missingFields.slice(0, 3).map((field, index) => (
                  <span
                    key={index}
                    className="inline-block px-2 py-1 bg-primary/20 text-primary text-xs rounded-full"
                  >
                    {field}
                  </span>
                ))}
                {missingFields.length > 3 && (
                  <span className="inline-block px-2 py-1 bg-primary/20 text-primary text-xs rounded-full">
                    +{missingFields.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

'use client';

import { useEffect, useState } from 'react';
import './typo.css';
import { markdownAsHtml2 } from "@/utils/markdownConverter";
import OnThisPage from '@/components/blocks/on-this-page';

export default function BlogPage() {
  const [blog, setBlog] = useState({
    title: 'Best 10 AI Tools Dominating 2025: What You Need to Know',
    description: 'This article highlights the year’s most influential platforms—from ChatGPT and Claude to Midjourney, Runway, and LLaMA—covering AI’s impact on creativity, business, research, and productivity, with trends pointing toward multimodal, specialized, and ethical AI.',
    author: {
      firstName: 'Vidya',
      lastName: 'Raner'
    },
    publishedAt: '10 August 2025',
    content: ''
  });

  useEffect(() => {
    const fetchContent = async () => {
      const markdownUrl = `http://localhost:3000/blogs/sample.md`;
      const { html } = await markdownAsHtml2({ url: markdownUrl, title: blog.title });
      setBlog({ ...blog, content: html })
    }
    fetchContent();
  }, []);

  return (
    <main className='flex gap-8 items-start'>
      <div className="prose dark:prose-invert max-w-3xl mx-auto p-4">
        <h1 className="text-4xl font-bold mb-4">{blog.title}</h1>
        {

        }
        <p className="text-base mb-2 border-l-4 border-gray-500 pl-4 italic">&quot;{blog.description}&quot;</p>
        <div className="flex gap-2">
          <p className="text-sm text-gray-500 mb-4 italic">By {blog?.author?.firstName || ''} {blog?.author?.lastName || ''}</p>
          <p className="text-sm text-gray-500 mb-4">{blog?.publishedAt}</p>
        </div>
        <div dangerouslySetInnerHTML={{ __html: blog.content }} />
      </div>
      <OnThisPage htmlContent={blog.content} />
    </main>
  );
}

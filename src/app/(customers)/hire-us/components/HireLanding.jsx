import { Badge } from '@/components/ui/badge';
import { motion } from 'motion/react';

export default function HireLanding() {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="section-modern bg-gradient-to-br from-primary/5 via-background to-primary/10"
    >
      <div className="container-modern">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Badge variant="outline" className="px-4 py-2 text-sm mb-6">
              Professional Services
            </Badge>
            <h1 className="text-display mb-8 text-shimmer">
              Let's Build Something <span className="text-gradient-primary">Amazing Together</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Partner with TechCulture's expert team to elevate your content strategy, grow your audience,
              and build meaningful digital experiences that drive results.
            </p>
          </motion.div>
        </div>
      </div>
    </motion.section>
  )
};

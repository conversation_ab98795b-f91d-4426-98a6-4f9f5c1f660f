import { BookO<PERSON>, TrendingUp, Users } from 'lucide-react';
import { motion } from 'motion/react';

export default function CategoriesLanding({ BLOGS, categories }) {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="section-modern bg-gradient-to-br from-primary/5 via-background to-primary/10"
    >
      <div className="container-modern text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h1 className="text-display mb-6 text-shimmer">
            Explore Categories
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Dive deep into your areas of interest. From cutting-edge AI to lifestyle tips,
            we've organized our content to help you find exactly what you're looking for.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
            <span className="flex items-center gap-1">
              <BookOpen className="w-4 h-4" />
              {BLOGS.length} Total Articles
            </span>
            <span className="flex items-center gap-1">
              <Users className="w-4 h-4" />
              {categories.length} Categories
            </span>
            <span className="flex items-center gap-1">
              <TrendingUp className="w-4 h-4" />
              Updated Daily
            </span>
          </div>
        </motion.div>
      </div>
    </motion.section>
  )
};

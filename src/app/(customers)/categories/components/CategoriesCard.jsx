import { motion } from 'motion/react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowUpRight } from 'lucide-react';
import Link from 'next/link';

export default function CategoriesCard({ slug, data, index }) {
  return (
    <motion.div
      key={slug}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -8 }}
      className="group"
    >
      <Link href={`/category/${slug}`}>
        <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm cursor-pointer">
          {/* Category Header */}
          <div className={`h-16 bg-gradient-to-br ${data.color} relative overflow-hidden`}>
            <div className="absolute inset-0 bg-black/20" />
            <div className="absolute top-4 left-4 text-4xl flex items-center gap-4">
              <div className="group-hover:scale-105 transition-transform">{data.icon}</div>
              <h3 className="text-xl font-bold text-white group-hover:scale-105 transition-transform duration-300">
                {data.title}
              </h3>
            </div>
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                <ArrowUpRight className="w-5 h-5 text-white" />
              </div>
            </div>
          </div>

          <CardContent className="px-4">
            <p className="text-muted-foreground mb-6 line-clamp-3 leading-relaxed">
              {data.description}
            </p>

            {/* Popular Topics */}
            <div className="mb-6">
              <p className="text-sm font-medium mb-3">Popular Topics:</p>
              <div className="flex flex-wrap gap-2">
                {data.topics.slice(0, 3).map((topic) => (
                  <Badge
                    key={topic}
                    variant="secondary"
                    className="text-xs"
                  >
                    {topic}
                  </Badge>
                ))}
                {data.topics.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{data.topics.length - 3} more
                  </Badge>
                )}
              </div>
            </div>

            {/* CTA */}
            <Button
              className="w-full btn-modern group-hover:scale-105 transition-transform duration-300"
              variant="outline"
            >
              Explore {data.title}
              <ArrowUpRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      </Link>
    </motion.div>
  )
};

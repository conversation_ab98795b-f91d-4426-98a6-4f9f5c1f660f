import { motion } from 'motion/react';
import CategoriesCard from './CategoriesCard';

export default function CategoriesGrid({ categories, }) {

  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern"
    >
      <div className="container-modern">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map(([slug, data], index) => (
            <CategoriesCard key={index} slug={slug} data={data} index={index} />
          ))}
        </div>
      </div>
    </motion.section>
  )
};

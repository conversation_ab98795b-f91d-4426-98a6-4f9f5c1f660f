'use client';

import { CATEGORY_CONTENT } from '@/constants/categoryContent';
import { BLOGS } from '@/constants/blogs';
import CategoriesLanding from './CategoriesLanding';
import CategoriesGrid from './CategoriesGrid';
import CategoriesFeatured from './CategoriesFeatured';

export function CategoriesPageClient() {
  const categories = Object.entries(CATEGORY_CONTENT);
  const featuredCategories = categories
    .sort((a, b) => parseInt(b[1].stats.readers) - parseInt(a[1].stats.readers))
    .slice(0, 3);

  return (
    <>
      {/* Hero Section */}
      <CategoriesLanding BLOGS={BLOGS} categories={categories} />

      {/* Categories Grid */}
      <CategoriesGrid categories={categories} />

      {/* Featured Categories */}
      <CategoriesFeatured featuredCategories={featuredCategories} />
    </>
  );
};

import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import {
  Users,
} from 'lucide-react';

export default function AuthorsLanding() {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="section-modern min-h-[70dvh] bg-gradient-to-br from-primary/5 via-background to-primary/10"
    >
      <div className="container-modern">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Badge variant="outline" className="px-4 py-2 text-sm mb-6">
              <Users className="w-4 h-4 mr-2" />
              Our Authors
            </Badge>
            <h1 className="text-display mb-8 text-shimmer">
              Meet Our <span className="text-gradient-primary">Expert Authors</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Discover the brilliant minds behind TechCulture's content. Our diverse team of writers,
              technologists, and thought leaders brings you insights from the cutting edge of innovation.
            </p>
          </motion.div>
        </div>
      </div>
    </motion.section>
  )
};

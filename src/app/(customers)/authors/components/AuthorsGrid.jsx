import { motion } from 'motion/react';
import { Card, CardContent, } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Search,
  Star,
  MapPin,
  ArrowUpRight,
} from 'lucide-react';
import Link from 'next/link';

export default function AuthorsGrid({ filteredAuthors, setSearchQuery, setSelectedExpertise, }) {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern"
    >
      <div className="container-modern">
        {filteredAuthors.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-16"
          >
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No authors found</h3>
            <p className="text-muted-foreground mb-6">
              Try adjusting your search criteria or filters to find more authors.
            </p>
            <Button
              onClick={() => {
                setSearchQuery('');
                setSelectedExpertise('');
              }}
              variant="outline"
            >
              Clear Filters
            </Button>
          </motion.div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredAuthors.map((author, index) => (
              <motion.div
                key={author.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
                  <div className="relative">
                    <div className="h-32 bg-gradient-to-br from-primary/10 to-primary/5"></div>
                    <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
                      <Avatar className="w-24 h-24 ring-4 ring-background group-hover:ring-primary/20 transition-all duration-300">
                        <AvatarImage src={author.avatar} alt={`${author.first_name} ${author.last_name}`} />
                        <AvatarFallback className="text-lg">
                          {author.first_name.charAt(0)}{author.last_name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </div>

                  <CardContent className="pt-16 pb-6 text-center">
                    <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300">
                      {author.first_name} {author.last_name}
                    </h3>
                    <Badge variant="secondary" className="mb-4">
                      {author.role}
                    </Badge>

                    {author.location && (
                      <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-3">
                        <MapPin className="w-3 h-3" />
                        {author.location}
                      </div>
                    )}

                    <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3">
                      {author.bio}
                    </p>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                      <div>
                        <div className="text-lg font-semibold text-primary">{author.articleCount}</div>
                        <div className="text-xs text-muted-foreground">Articles</div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-primary">{author.followers.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">Followers</div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-primary flex items-center justify-center gap-1">
                          {author.rating}
                          <Star className="w-3 h-3 fill-current" />
                        </div>
                        <div className="text-xs text-muted-foreground">Rating</div>
                      </div>
                    </div>

                    {/* Expertise tags */}
                    <div className="flex flex-wrap gap-1 justify-center mb-4">
                      {author.expertise.slice(0, 2).map((skill, skillIndex) => (
                        <Badge key={skillIndex} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {author.expertise.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{author.expertise.length - 2} more
                        </Badge>
                      )}
                    </div>

                    {/* Action button */}
                    <Link href={`/authors/${author.slug}`}>
                      <Button className="w-full btn-modern group">
                        View Profile
                        <ArrowUpRight className="w-4 h-4 ml-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.section>
  )
};

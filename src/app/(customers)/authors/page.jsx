'use client';

import { useState, useMemo } from 'react';
import { getAllAuthors } from '@/constants/authors';
import { Newsletter } from '@/components/sections/Newsletter';
import AuthorsLanding from './components/AuthorsLanding';
import AuthorsFilter from './components/AuthorsFilter';
import AuthorsGrid from './components/AuthorsGrid';

export default function AuthorsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedExpertise, setSelectedExpertise] = useState('');
  const [filteredAuthors, setFilteredAuthors] = useState([]);


  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <AuthorsLanding />

      {/* Filters Section */}
      <AuthorsFilter
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedExpertise={selectedExpertise}
        setSelectedExpertise={setSelectedExpertise}
        filteredAuthors={filteredAuthors}
        setFilteredAuthors={setFilteredAuthors}
      />

      {/* Authors Grid */}
      <AuthorsGrid filteredAuthors={filteredAuthors} setSearchQuery={setSearchQuery} setSelectedExpertise={setSelectedExpertise} />

      {/* Newsletter Section */}
      <Newsletter />
    </div>
  );
}

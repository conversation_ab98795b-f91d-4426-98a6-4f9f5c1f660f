'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Copy, Share2 } from 'lucide-react'

export function ShareButton({ author, socialShareLinks }) {
  const [open, setOpen] = useState(false)
  const shareUrl = typeof window !== 'undefined' ? window.location.href : ''
  const fullName = `${author.first_name} ${author.last_name}`

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${fullName} - TechCulture Author`,
          text: author.bio,
          url: shareUrl,
        })
      } catch (err) {
        console.log('Error sharing:', err)
      }
    } else {
      setOpen(true) // open custom share dialog
    }
  }

  const handleCopy = async () => {
    await navigator.clipboard.writeText(shareUrl)
  }

  return (
    <>
      <Button variant={'outline'} onClick={handleShare}>
        Share Profile
        <Share2 className="w-4 h-4 mr-2" />
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share this page</DialogTitle>
          </DialogHeader>

          <div className="flex flex-col gap-4">
            <div className="flex items-center space-x-2">
              <Input value={shareUrl} readOnly />
              <Button variant="outline" onClick={handleCopy}>
                <Copy className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid md:grid-cols-3 grid-cols-1 gap-2">
              {
                socialShareLinks?.map((social, index) => (
                  <a
                    key={index}
                    href={social?.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button variant="ghost">
                      <social.icon className="w-4 h-4 mr-2" /> {social?.label}
                    </Button>
                  </a>
                ))
              }
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

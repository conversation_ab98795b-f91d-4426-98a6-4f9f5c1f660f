'use client';

import { useState, useMemo } from 'react';
import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  MapPin,
  BookOpen,
  Users,
  Award,
  Briefcase,
  Heart,
  Mail,
  Globe,
  Github,
  Linkedin,
  Twitter,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { getAuthorBySlug } from '@/constants/authors';
import { BLOGS } from '@/constants/blogs';
import { Newsletter } from '@/components/sections/Newsletter';
import { notFound } from 'next/navigation';
import { ShareButton } from './components/ShareButton';
import AuthorProfileArticles from './components/AuthorProfileArticles';
import AuthorProfileAbout from './components/AuthorProfileAbout';
import AuthorProfileExperience from './components/AuthorProfileExperience';
import AuthorProfileAchievements from './components/AuthorProfileAchievements';

export default function AuthorProfilePage() {
  const params = useParams();
  const router = useRouter();
  const [isFollowing, setIsFollowing] = useState(false);

  const author = getAuthorBySlug(params.slug);

  if (!author) {
    notFound();
  }

  // Get author's articles
  const authorArticles = useMemo(() => {
    return BLOGS.filter(blog =>
      blog.author.id === author.id
    ).sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
  }, [author.id]);

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  const socialLinks = [
    { icon: Twitter, url: `https://twitter.com/${author.social.twitter}`, label: 'Twitter', color: 'hover:text-blue-500' },
    { icon: Linkedin, url: `https://linkedin.com/in/${author.social.linkedin}`, label: 'LinkedIn', color: 'hover:text-blue-700' },
    { icon: Github, url: `https://github.com/${author.social.github}`, label: 'GitHub', color: 'hover:text-gray-700' },
    { icon: Globe, url: author.social.website, label: 'Website', color: 'hover:text-green-500' },
    { icon: Mail, url: `mailto:${author.social.email}`, label: 'Email', color: 'hover:text-red-500' }
  ].filter(link => link.url);

  return (
    <div className="flex flex-col">
      {/* Back Navigation */}
      <div className="container-modern py-4">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 hover:bg-muted/50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Authors
        </Button>
      </div>

      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="section-modern bg-gradient-to-br from-primary/5 via-background to-primary/10"
      >
        <div className="container-modern">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              {/* Author Avatar */}
              <div className="relative inline-block mb-6">
                <Avatar className="w-32 h-32 ring-4 ring-background shadow-2xl">
                  <AvatarImage src={author.avatar} alt={`${author.first_name} ${author.last_name}`} />
                  <AvatarFallback className="text-3xl">
                    {author.first_name.charAt(0)}{author.last_name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-background flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                </div>
              </div>

              {/* Author Info */}
              <h1 className="text-4xl md:text-5xl font-bold mb-4 text-shimmer">
                {author.first_name} <span className="text-gradient-primary">{author.last_name}</span>
              </h1>

              <Badge variant="secondary" className="text-md px-4 py-2 mb-4">
                {author.role}
              </Badge>

              {author.location && (
                <div className="flex items-center justify-center gap-2 text-muted-foreground mb-6">
                  <MapPin className="w-4 h-4" />
                  {author.location}
                </div>
              )}

              <p className="md:text-xl text-sm text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
                {author.longBio || author.bio}
              </p>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-4 justify-center mb-8">
                <Button
                  onClick={handleFollow}
                  className={`btn-modern ${isFollowing ? 'bg-green-500 hover:bg-green-600' : ''}`}
                >
                  <Heart className={`w-4 h-4 mr-2 ${isFollowing ? 'fill-current' : ''}`} />
                  {isFollowing ? 'Following' : 'Follow'}
                </Button>
                <ShareButton author={author} socialShareLinks={socialLinks} />
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Content Tabs */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern"
      >
        <div className="container-modern">
          <Tabs defaultValue="articles" className="w-full">
            {/* Fixed Tabs List */}
            <div className="mb-8 overflow-x-auto">
              <TabsList className="inline-flex h-12 items-center justify-start rounded-lg bg-gray-100 p-1 w-full min-w-max">
                <TabsTrigger
                  value="articles"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm min-w-max"
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  <span className="md:flex hidden">Articles</span>
                  <span className="">({authorArticles.length})</span>
                </TabsTrigger>
                <TabsTrigger
                  value="about"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm min-w-max"
                >
                  <Users className="w-4 h-4 mr-2" />
                  <span className="md:flex hidden">About</span>
                </TabsTrigger>
                <TabsTrigger
                  value="experience"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm min-w-max"
                >
                  <Briefcase className="w-4 h-4 mr-2" />
                  <span className="md:flex hidden">Experience</span>
                </TabsTrigger>
                <TabsTrigger
                  value="achievements"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm min-w-max"
                >
                  <Award className="w-4 h-4 mr-2" />
                  <span className="md:flex hidden">Achievements</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Articles Tab */}
            <TabsContent value="articles" className="space-y-8">
              <AuthorProfileArticles authorArticles={authorArticles} />
            </TabsContent>

            {/* About Tab */}
            <TabsContent value="about" className="space-y-8">
              <AuthorProfileAbout author={author} socialLinks={socialLinks} />
            </TabsContent>

            {/* Experience Tab */}
            <TabsContent value="experience" className="space-y-8">
              <AuthorProfileExperience author={author} />
            </TabsContent>

            {/* Achievements Tab */}
            <TabsContent value="achievements" className="space-y-8">
              <AuthorProfileAchievements author={author} />
            </TabsContent>
          </Tabs>
        </div>
      </motion.section>

      {/* Newsletter Section */}
      <Newsletter />
    </div>
  );
}

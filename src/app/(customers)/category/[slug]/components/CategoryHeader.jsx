import {
  ArrowLeft,
} from 'lucide-react';
import Link from 'next/link';

export default function CategoryHeader({ filteredBlogs, categoryData, }) {
  return (
    <div className="border-b bg-background/95 backdrop-blur-sm top-16 md:top-20 z-40">
      <div className="container-modern py-4 md:py-6">
        <div className="flex items-center gap-3 mb-4 md:mb-6">
          <Link href="/categories" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Back to Categories</span>
            <span className="sm:hidden">Back</span>
          </Link>
          <span className="text-muted-foreground">•</span>
          <span className="text-sm text-muted-foreground">{filteredBlogs.length}  {filteredBlogs?.length === 1 ? 'article' : "articles"}</span>
        </div>

        {/* Category Header Card */}
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-4 md:p-6 mb-4 md:mb-6">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 md:w-12 md:h-12 bg-primary rounded-lg flex items-center justify-center text-primary-foreground text-lg md:text-xl font-bold">
              {categoryData.icon}
            </div>
            <div className="flex-1">
              <h1 className="text-xl md:text-2xl font-bold mb-2">{categoryData.title}</h1>
              <p className="text-muted-foreground text-sm md:text-base mb-4">{categoryData.description}</p>
              <div className="flex items-center gap-4 md:gap-6 text-xs md:text-sm text-muted-foreground">
                <span>{filteredBlogs?.length} {filteredBlogs?.length === 1 ? 'article' : "articles"}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
};

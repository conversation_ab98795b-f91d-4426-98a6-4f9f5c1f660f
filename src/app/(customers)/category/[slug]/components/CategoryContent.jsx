import { motion } from 'motion/react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Clock,
  Eye,
} from 'lucide-react';
import Link from 'next/link';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';

export default function CategoryContent({ isMobile, filteredBlogs }) {
  return (
    <div className={isMobile ? "col-span-1" : "lg:col-span-3"}>
      <div className="space-y-6">
        {filteredBlogs.map((blog, index) => (
          <motion.div
            key={blog.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Link href={`/blog/${blog.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group -p-2">
                <div className="flex flex-col lg:flex-row h-full">
                  {/* Fixed Image Container */}
                  <div className="w-full lg:w-80 h-48 lg:h-auto relative overflow-hidden flex-shrink-0">
                    <LazyLoadingImage
                      src={blog.image}
                      alt={blog.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>

                  {/* Content */}
                  <div className="flex-1 p-4 lg:p-6 flex flex-col">
                    <div className="flex items-start justify-between mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {blog.category}
                      </Badge>
                    </div>
                    <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold mb-2 group-hover:text-primary transition-colors line-clamp-2`}>
                      {blog.title}
                    </h3>
                    <p className={`text-muted-foreground text-sm mb-4 ${isMobile ? 'line-clamp-3' : 'line-clamp-2'}`}>
                      {blog.description}
                    </p>
                    {/* Tags */}
                    {blog.tags && blog.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {blog.tags.slice(0, 3).map((tag) => (
                          <span key={tag} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-md">
                            {tag}
                          </span>
                        ))}
                        {blog.tags.length > 3 && (
                          <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                            +{blog.tags.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                    {/* Meta Information */}
                    <div className="flex flex-col gap-3">
                      <div className={isMobile ? "flex items-center gap-3" : "flex items-center gap-3"}>
                        <Avatar className="w-6 h-6">
                          <AvatarImage src={blog.author.avatar} />
                          <AvatarFallback className="text-xs">
                            {blog.author.first_name[0]}{blog.author.last_name[0]}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-muted-foreground">{blog.author.first_name} {blog.author.last_name}</span>
                      </div>
                      <div className={`flex items-center gap-3 text-sm text-muted-foreground ${isMobile ? 'justify-start' : 'gap-4'}`}>
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {blog.readTime}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {blog.views}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </Link>
          </motion.div>
        ))}
      </div>
    </div>
  )
}


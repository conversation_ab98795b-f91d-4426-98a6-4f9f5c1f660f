'use client';

import { useState } from 'react';
import { useIsMobile } from '@/components/ui/use-mobile';
import { BLOGS } from '@/constants/blogs';
import CategoryHeader from './CategoryHeader';
import CategoryContent from './CategoryContent';
import CategoryFilter from './CategoryFilter';
import CategoryMobileFilter from './CategoryMobileFilter';

export function CategoryPageClient({ categoryData, slug }) {
  const isMobile = useIsMobile();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [selectedAuthor, setSelectedAuthor] = useState('All Authors');

  const categoryBlogs = BLOGS.filter(blog =>
    blog.category.toLowerCase() === slug?.toLowerCase()
  );

  // Get unique authors from category blogs
  const categoryAuthors = [...new Set(categoryBlogs.map(blog => `${blog.author.first_name} ${blog.author.last_name}`))];

  // Get all unique tags from blogs in this category
  const availableTags = [...new Set(
    categoryBlogs
      .filter(blog => blog.tags && Array.isArray(blog.tags))
      .flatMap(blog => blog.tags)
  )].sort();

  // Filter blogs based on search and filters
  const filteredBlogs = categoryBlogs.filter(blog => {
    const authorName = `${blog.author.first_name} ${blog.author.last_name}`;
    const matchesSearch = searchQuery === '' ||
      blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      blog.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTags = selectedTags.length === 0 ||
      selectedTags.some(tag => blog.tags?.includes(tag));

    const matchesAuthor = selectedAuthor === 'All Authors' || authorName === selectedAuthor;

    return matchesSearch && matchesTags && matchesAuthor;
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <CategoryHeader filteredBlogs={filteredBlogs} categoryData={categoryData} />

      {/* Mobile Filter/Sort Bar */}
      {isMobile && (
        <CategoryMobileFilter
          selectedTags={selectedTags}
          selectedAuthor={selectedTags}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          setSelectedTags={setSelectedTags}
          availableTags={availableTags}
          setSelectedAuthor={setSelectedAuthor}
          categoryAuthors={categoryAuthors}
          filteredBlogs={filteredBlogs}
        />
      )}

      {/* Main Content */}
      <div className="container-modern py-4 md:py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 md:gap-6">
          {/* Left Sidebar - Filters - Hidden on mobile */}
          {!isMobile && (
            <CategoryFilter
              selectedTags={selectedTags}
              setSelectedTags={setSelectedTags}
              selectedAuthor={selectedTags}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              availableTags={availableTags}
              setSelectedAuthor={setSelectedAuthor}
              categoryAuthors={categoryAuthors}
            />
          )}

          {/* Main Content - Articles */}
          <CategoryContent isMobile={isMobile} filteredBlogs={filteredBlogs} />
        </div>
      </div>
    </div>
  );
};

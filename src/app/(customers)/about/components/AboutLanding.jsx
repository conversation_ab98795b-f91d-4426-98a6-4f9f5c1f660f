import { Badge } from "@/components/ui/badge";
import { CompanyName } from "@/constants/companyName";
import { motion } from "motion/react";

export default function AboutLanding() {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="section-modern bg-gradient-to-br from-primary/5 via-background to-primary/10"
    >
      <div className="container-modern">
        <div className="max-w-5xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-8"
          >
            <Badge variant="outline" className="px-4 py-2 text-sm mb-6">
              Our Story
            </Badge>
            <h1 className="text-display mb-8 text-shimmer">
              {CompanyName}
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-4xl mx-auto leading-relaxed">
              We're on a mission to democratize technology knowledge and create the world's most engaging platform
              for sharing insights, stories, and innovations. Founded in 2025, TechCulture has grown into a thriving
              community of writers and readers who believe in the power of accessible, quality content.
            </p>
          </motion.div>
        </div>
      </div>
    </motion.section>
  )
};

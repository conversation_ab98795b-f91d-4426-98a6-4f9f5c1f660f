'use client';

import { Heart, Target, Users, Zap, Award, Globe, Rocket } from "lucide-react"
import { Newsletter } from "@/components/sections/Newsletter"
import AboutLanding from "./components/AboutLanding";
import AboutMissions from "./components/AboutMissions";
// import AboutJourney from "./components/AboutJourney";
import AboutValues from "./components/AboutValues";
import AboutTeam from "./components/AboutTeam";

export default function AboutPage() {
  const values = [
    {
      icon: Heart,
      title: "Passion for Quality",
      description: "We believe in creating and sharing content that truly matters and adds value to our readers' lives.",
      color: "text-red-500"
    },
    {
      icon: Users,
      title: "Community First",
      description: "Our platform is built around fostering meaningful connections between writers and readers.",
      color: "text-blue-500"
    },
    {
      icon: Target,
      title: "Purpose Driven",
      description: "Every article we publish serves a purpose - to educate, inspire, or entertain our community.",
      color: "text-green-500"
    },
    {
      icon: Zap,
      title: "Innovation",
      description: "We continuously evolve our platform to provide the best reading and writing experience.",
      color: "text-purple-500"
    },
  ]

  const timeline = [
    {
      year: "2024",
      title: "The Beginning",
      description: "TechCulture was founded with a vision to democratize tech knowledge and create a platform where innovation meets accessibility.",
      icon: Rocket,
      color: "bg-blue-500"
    },
    {
      year: "2024",
      title: "Community Growth",
      description: "Reached 10,000 active readers and published over 500 high-quality articles across various tech domains.",
      icon: Users,
      color: "bg-green-500"
    },
    {
      year: "2024",
      title: "Global Recognition",
      description: "Featured in major tech publications and recognized as one of the fastest-growing tech blogs.",
      icon: Award,
      color: "bg-purple-500"
    },
    {
      year: "Future",
      title: "What's Next",
      description: "Expanding into video content, podcasts, and interactive learning experiences for our community.",
      icon: Globe,
      color: "bg-orange-500"
    }
  ]

  return (
    <>
      {/* Hero Section */}
      <AboutLanding />

      {/* Mission Section */}
      <AboutMissions />

      {/* Timeline Section */}
      {/*
      <AboutJourney timeline={timeline} />
      */}

      {/* Values Section */}
      <AboutValues values={values} />

      {/* Team Section */}
      <AboutTeam />

      {/* Newsletter Section */}
      <Newsletter />
    </>
  )
};

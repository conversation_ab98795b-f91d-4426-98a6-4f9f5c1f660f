'use client';

import BlogsList from "@/components/sections/home/<USER>";
import { AdvancedFilter } from "@/components/sections/home/<USER>";
import { HeroCarousel } from "@/components/sections/home/<USER>";
import { Button } from "@/components/ui/button";
import { BLOGS } from "@/constants/blogs";
import { ArrowUpRight } from "lucide-react";
import { useState, useCallback } from "react";
import { motion } from "motion/react";
import { StructuredData } from "@/components/seo/StructuredData";
import { FeaturedPosts } from "@/components/sections/home/<USER>";
import { ReadingProgress } from "@/components/ui/ReadingProgress";
import { Newsletter } from "@/components/sections/Newsletter";

export default function page() {
  const [filteredBlogs, setFilteredBlogs] = useState(BLOGS);

  // Get featured posts (top 3 most viewed)
  const featuredPosts = BLOGS.sort((a, b) => parseFloat(b.views) - parseFloat(a.views)).slice(0, 3);

  const handleFilterChange = useCallback((filters) => {
    let filtered = [...BLOGS];

    // Apply search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(query) ||
        blog.description.toLowerCase().includes(query) ||
        blog.category.toLowerCase().includes(query) ||
        `${blog.author.first_name} ${blog.author.last_name}`.toLowerCase().includes(query)
      );
    }

    // Apply category filters
    if (filters.categories && filters.categories.length > 0) {
      filtered = filtered.filter(blog => filters.categories.includes(blog.category));
    }

    // Apply reading time filter
    if (filters.readTimeRange) {
      filtered = filtered.filter(blog => {
        const readTime = parseInt(blog.readTime.replace(' min read', ''));
        return readTime >= filters.readTimeRange[0] && readTime <= filters.readTimeRange[1];
      });
    }

    // Apply date range filter
    if (filters.dateRange && filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (filters.dateRange) {
        case 'today':
          filterDate.setDate(now.getDate() - 1);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      filtered = filtered.filter(blog => new Date(blog.publishedAt) >= filterDate);
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'latest':
        filtered.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.publishedAt) - new Date(b.publishedAt));
        break;
      case 'popular':
        filtered.sort((a, b) => parseFloat(b.views) - parseFloat(a.views));
        break;
      case 'shortest':
        filtered.sort((a, b) => parseInt(a.readTime) - parseInt(b.readTime));
        break;
      case 'longest':
        filtered.sort((a, b) => parseInt(b.readTime) - parseInt(a.readTime));
        break;
    }

    setFilteredBlogs(filtered);
  }, []);


  return (
    <div className="overflow-x-hidden">
      <StructuredData type="website" />
      <StructuredData type="organization" />
      <ReadingProgress estimatedReadTime={8} />

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="relative"
      >
        <HeroCarousel />
      </motion.div>

      {/* Featured Posts Section */}
      <FeaturedPosts posts={featuredPosts} />

      {/* Advanced Search & Filter */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern border-b glass"
      >
        <div className="container-modern">
          <div className="text-center mb-12">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-headline mb-4 text-shimmer"
            >
              Discover Your Perfect Read
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-muted-foreground text-lg max-w-2xl mx-auto"
            >
              Use our advanced search and filtering system to find exactly what you're looking for
            </motion.p>
          </div>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <AdvancedFilter
              onFilterChange={handleFilterChange}
              totalResults={filteredBlogs.length}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Featured Blogs */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="bg-gradient-to-br from-muted/30 via-background to-muted/20"
      >
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="py-6"
          >
            <BlogsList blogs={filteredBlogs.slice(0, 8)} />
          </motion.div>
          <div className="flex flex-col items-center justify-center mb-6">
            {filteredBlogs.length > 6 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <Button variant="outline" className="btn-modern group">
                  <span>Explore More Articles</span>
                  <ArrowUpRight className="w-4 h-4 ml-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </Button>
              </motion.div>
            )}
          </div>
        </div>
      </motion.section>

      {/* Newsletter Section */}
      <Newsletter />
    </div>
  )
};
